<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Logo Edit</title>
    <style>
        body.edit-mode .editable-component {
            border: 2px solid #dc3545 !important;
            border-radius: 6px;
            margin: 5px;
            padding: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            background-color: transparent !important;
        }
        
        body.edit-mode .editable-component:hover {
            background-color: rgba(220, 53, 69, 0.1) !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.2);
            border-color: #c82333 !important;
        }
        
        body.edit-mode .editable-component * {
            pointer-events: none;
        }
        
        body.edit-mode .editable-component {
            pointer-events: auto;
        }
        
        .logo {
            display: inline-block;
            padding: 20px;
        }
        
        .header-logo {
            max-height: 60px;
        }
    </style>
</head>
<body class="edit-mode">
    <div id="logo" class="logo editable-component" data-component-type="logo">
        <a href="/" title="Website" rel="home">
            <img src="https://via.placeholder.com/200x60/007bff/ffffff?text=LOGO" class="header-logo" alt="Logo">
        </a>
    </div>
    
    <button onclick="toggleEditMode()">Toggle Edit Mode</button>
    
    <script>
        function toggleEditMode() {
            document.body.classList.toggle('edit-mode');
        }
        
        document.addEventListener('click', (e) => {
            if (document.body.classList.contains('edit-mode')) {
                const component = e.target.closest('.editable-component');
                if (component) {
                    console.log('🖱️ Clicked editable component:', component);
                    console.log('🔍 Component classes:', component.className);
                    console.log('🔍 Component ID:', component.id);
                    console.log('🔍 Component data-component-type:', component.dataset.componentType);
                    e.preventDefault();
                    e.stopPropagation();
                    alert('Logo clicked! Component type: ' + (component.dataset.componentType || 'unknown'));
                }
            }
        });
    </script>
</body>
</html>
