@extends('layouts.layouts')

@section('title', 'Tra cứu văn bằng chứng chỉ')

@section('content')
   <div class="main-content-inner">
    <div class="page-content">
        <div class="body-activity" id="Div1">
            <div id="DivMain">
                <div class="row">
                    <div class="khung-vien-tim-kiem" style="background: #ffffff;">
                        <div class="row" style="margin-top: 10px;margin-bottom: 0px;">
                            <h1 class="text-center nts-text-backround" style="font-weight: bold; text-transform: uppercase">
                                Tra cứu văn bằng chứng chỉ
                            </h1>
                        </div>
                        <div class="row" style="margin-bottom: 16px">
                            <div class="col-md-3"></div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input style="border-right: 0;height: 32px !important; border-top-right-radius: 0 !important; border-bottom-right-radius: 0 !important;" class="form-control input-sm" type="text" id="timKiemG" placeholder="Nội dung tìm kiếm" autocomplete="off">
                                    <span class="input-group-addon" style="margin: 0; padding: 0; border: none;">
                                        <button class="btn btn-white btn-sm nts-secondary" id="btnTimKiem" style="border-top-left-radius: 0 !important; border-bottom-left-radius: 0 !important; width: 35px; height: 32px "><span class="fas fa-search" aria-hidden="true"></span></button>
                                        <button class="btn btn-primary btn-sm nts-khac nts-color-timkiem" id="TimKiemNangCao" style="border-top-left-radius: 0 !important; border-bottom-left-radius: 0 !important; width: 35px; height: 32px; margin-left: -4px;"><span class="fas fa-sliders-h" aria-hidden="true"></span></button>
                                    </span>
                                </div>
                                <div id="KhungTimKiem" style="width: 43%; z-index: 99; position: absolute; margin-top: 4px; background: rgb(255, 255, 255); padding: 12px 12px 8px; border: 1px solid rgb(229, 229, 229); border-radius: 4px; box-shadow: rgba(0, 0, 0, 0.2) 0px 5px 15px; display: none;">
                                    <div class="row" style=" margin-bottom: 4px">
                                        <div class="col-md-6">
                                            <span style="font-size: 16px;" class="nts-secondary"><i class="fa fa-search"></i>&ensp;Tìm kiếm nâng cao</span>
                                        </div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="" for="TuNgay_TimKiem" style="margin-bottom: 0px;">Từ ngày</label>
                                                <div class="input-group">
                                                    <div class="input-group"><input type="text" class="form-control date-picker" id="TuNgay_TimKiem" data-date-format="dd/mm/yyyy" autocomplete="off" placeholder="dd/MM/yyyy">
                                                        {{-- <span class="input-group-text cursor-pointer"><i class="fa fa-calendar"></i></span> --}}
                                                    </div>
                                                    <i class="fa-solid fa-calendar-days"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="" for="DenNgay_TimKiem" style="margin-bottom: 0px;">Đến ngày</label>
                                                <div class="input-group">
                                                    <div class="input-group"><input type="text" class="form-control date-picker" id="DenNgay_TimKiem" data-date-format="dd/mm/yyyy" autocomplete="off" placeholder="dd/MM/yyyy">
                                                        {{-- <span class="input-group-text cursor-pointer"><i class="fa fa-calendar"></i></span> --}}
                                                    </div>
                                                    <i class="fa-solid fa-calendar-days"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-md-6">
                                            <label class="form-label" for="QuyetDinhID_Loc">Quyết định</label>
                                            <select class="form-control input-sm" id="QuyetDinhID_Loc" tabindex="0">
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label" for="SoGocID_Loc">Sổ gốc</label>
                                            <select class="form-control input-sm" id="SoGocID_Loc" tabindex="0">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-12">
                                            <label class="form-label" for="TiepNhanPhoiVBCCID_Loc">Phôi bằng</label>
                                            <select class="form-control input-sm" id="TiepNhanPhoiVBCCID_Loc" tabindex="0">
                                            </select>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group" style="text-align: right">
                                                <a href="#" class="nts-color-dong btn btn-outline-danger" id="DongTimKiem"><i class="fa fa-close"></i>&ensp;Đóng</a>
                                                <button type="button" id="TimKiem" class="btn btn-sm btn-success nts-color-timkiem"><i class="fa fa-search"></i>&ensp;Tìm kiếm</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3"></div>
                        </div>

                        <div class="col-12" id="DivThongKe">
                            <div class="row row-cards">
                                <div class="col-sm-6 col-lg-3">
                                    <div class="card card-sm card-margin-0">
                                        <div class="card-body card-buoc-tra-cuu-s1" style="background-color: #1C6BA7">
                                            <div class="row align-items-center" style="padding-left: 10px;">
                                                <div class="col-auto">
                                                    <span class="text-white card-stt-buoc-tra-cuu">
                                                        1
                                                    </span>
                                                </div>
                                                <div class="col">
                                                    <div class="text-white card-tieu-de-buoc-tra-cuu">
                                                        NHẬP THÔNG TIN
                                                    </div>
                                                    <div class="text-white">
                                                        Nhập thông tin cần tra cứu
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-lg-3">
                                    <div class="card card-sm card-margin-0">
                                        <div class="card-body card-buoc-tra-cuu-s2" style="background-color: #1A9DBC">
                                            <div class="row align-items-center" style="padding-left: 10px;">
                                                <div class="col-auto">
                                                    <span class="text-white card-stt-buoc-tra-cuu">
                                                        2
                                                    </span>
                                                </div>
                                                <div class="col">
                                                    <div class="text-white card-tieu-de-buoc-tra-cuu">
                                                        TRA CỨU NÂNG CAO
                                                    </div>
                                                    <div class="text-white">
                                                        Nhấn <i class="fa fas fa-sliders-h" style="border: 1px solid #fff; padding: 4px; border-radius: 4px;"></i> để chọn các tham số tra cứu nâng cao (nếu có)
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-lg-3">
                                    <div class="card card-sm card-margin-0">
                                        <div class="card-body card-buoc-tra-cuu-s3" style="background-color: #EFA93C">
                                            <div class="row align-items-center" style="padding-left: 10px;">
                                                <div class="col-auto">
                                                    <span class="text-white card-stt-buoc-tra-cuu">
                                                        3
                                                    </span>
                                                </div>
                                                <div class="col">
                                                    <div class="text-white card-tieu-de-buoc-tra-cuu">
                                                        TRA CỨU
                                                    </div>
                                                    <div class="text-white">
                                                        Nhấn <i class="fa fas fa-search" style="border: 1px solid #fff; padding: 4px; border-radius: 4px;"></i> để tiến hành tra cứu
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-lg-3">
                                    <div class="card card-sm card-margin-0">
                                        <div class="card-body card-buoc-tra-cuu-s4" style="background-color: #14937E">
                                            <div class="row align-items-center" style="padding-left: 10px;">
                                                <div class="col-auto">
                                                    <span class="text-white card-stt-buoc-tra-cuu">
                                                        4
                                                    </span>
                                                </div>
                                                <div class="col">
                                                    <div class="text-white card-tieu-de-buoc-tra-cuu">
                                                        XEM KẾT QUẢ
                                                    </div>
                                                    <div class="text-white">
                                                        Xem kết quả sau khi tra cứu
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="DivTimKiem">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label" style="margin-top: 10px;font-weight: bold;">Tìm thấy <span id="lblSoLuongKetQua">0</span> kết quả tìm kiếm</label>
                    </div>
                    <div class="col-md-8">
                        <div class="text-end">
                            <div class="btn-group" style="height:40px">
                                <a id="btnAnHienTQ" style="margin-top:4px;padding:4px;cursor:pointer; color:#41d81e;" class=""><i id="iconAnHien" class="fa fa-eye-slash" aria-hidden="true"></i><span id="textAnHien"> Ẩn hướng dẫn tìm kiếm</span></a>
                            </div>
                            <span style="" class="btn position-relative">
                                <select class="form-control" id="CbSapXep" tabindex="0" style="padding: 0.05rem 0.55rem !important; cursor: pointer">
                                    <option value="NgayCapBang" selected="">Ngày cấp bằng</option>
                                </select>
                                <span id="BtnSapXepTangGiam" style="margin-left:4px;">
                                    <i class="fa fa-sort-alpha-asc" aria-hidden="true"></i>

                                </span>
                            </span>
                            <div class="btn-group" role="group">
                                <input type="radio" class="btn-check height-button-icon" title="Xem dạng lưới" name="btn-layout" id="btn-layout-1" autocomplete="off">
                                <label style="margin-bottom: 0px" for="btn-layout-1" class="btn btn-icon">
                                    <svg class="icon icon-tabler icon-tabler-list" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M9 6l11 0"></path>
                                        <path d="M9 12l11 0"></path>
                                        <path d="M9 18l11 0"></path>
                                        <path d="M5 6l0 .01"></path>
                                        <path d="M5 12l0 .01"></path>
                                        <path d="M5 18l0 .01"></path>
                                    </svg>
                                </label>
                                <input type="radio" class="btn-check height-button-icon" title="Xem dạng danh sách" name="btn-layout" id="btn-layout-2" autocomplete="off" checked="">
                                <label style="margin-bottom: 0px" for="btn-layout-2" class="btn btn-icon">
                                    <svg class="icon icon-tabler icon-tabler-list-details" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M13 5h8"></path>
                                        <path d="M13 9h5"></path>
                                        <path d="M13 15h8"></path>
                                        <path d="M13 19h5"></path>
                                        <path d="M3 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"></path>
                                        <path d="M3 14m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"></path>
                                    </svg>
                                </label>
                                &ensp;
                                <div class="btn-group btn-nts-them" style="">
                                    <div class="dropdown d-inline">
                                        <button class="btn btn-primary nts-color-them dropdown-toggle-hide-arrow height-button-icon nts-khac" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <i class="blue fa fa-ellipsis-h"></i>
                                        </button>
                                        <div class="dropdown-menu dropdown-menu-lg-end w-auto nts-border-secondary" style="left:-110px; top: 20px">
                                            <a class="dropdown-item textsize-item btntienich" href="javascript:void(0);" id="btnPrint" onclick="previewExportv1('pdf',GridMainLuoi);return false;"><i class="text-warning fa fa-print iconsize-item"></i>&ensp; In</a>
                                            <a class="dropdown-item textsize-item btntienich" href="javascript:void(0);" id="btnExport" onclick="previewExportv1('excel',GridMainLuoi);return false;"><i class="text-success fa fa-file-excel-o iconsize-item"></i>&ensp; Xuất Excel</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="DivDanhSach" style="margin-top: 4px">
                <div class="row">
                    <div id="GridMainDS" class=""></div>
                </div>
            </div>
            <div id="DivLuoi" style="display:none">
                <div class="row">
                    <div class="col-md-12">
                        <div id="GridMainLuoi" class="GridData"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="body-activity" id="Div1">
        {{-- <link href="/Content/GIS/leaflet.css" rel="stylesheet"> --}}

<style type="text/css">

    .card-buoc-tra-cuu-s1,
    .card-buoc-tra-cuu-s2,
    .card-buoc-tra-cuu-s3,
    .card-buoc-tra-cuu-s4 {
    min-height: 90px; /* Hoặc giá trị cao hơn nếu nội dung nhiều */
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 20px;
    border-radius: 8px;
}

.card-stt-buoc-tra-cuu {
    font-size: 24px;
    font-weight: bold;
}

.card-tieu-de-buoc-tra-cuu {
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 4px;
}

.card-margin-0 {
    margin-bottom: 16px; /* Giúp cách đều khi xuống mobile */
}

@media (min-width: 768px) {
    .card-margin-0 {
        margin-bottom: 0; /* Không cần margin nếu desktop 1 hàng */
    }
}

</style>


<style id="Modalcss">
    .modal-footer {
        flex-wrap: unset;
        padding-left: 20px;
    }

    .form-label {
        font-weight: unset;
    }

    #mdXemThongTinGiayCNGiayCK_us {
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 !important;
        padding-right: 0px !important;
        padding: 0 !important;
        scroll-behavior: smooth;
    }

        #mdXemThongTinGiayCNGiayCK_us .modal-content {
            min-height: 100vh !important;
        }

        #mdXemThongTinGiayCNGiayCK_us .modal-dialog {
            width: 100% !important;
            max-width: 100% !important;
            padding: 0 !important;
            margin: unset !important;
        }

    @media (max-width: 468px) {
        #mdXemThongTinGiayCNGiayCK_us .modal-dialog .modal-body {
            margin-bottom: 40px !important;
        }
    }

    #mdXemThongTinGiayCNGiayCK_us .modal-body {
        max-height: 95vh; /* Thiết lập chiều cao tối đa */
        overflow-y: scroll; /* Cho phép cuộn dọc */
        scrollbar-width: none; /* Ẩn thanh cuộn trên Firefox */
        -ms-overflow-style: none; /* Ẩn thanh cuộn trên IE và Edge */
    }

        /* Ẩn thanh cuộn trên Webkit (Chrome, Safari) */
        #mdXemThongTinGiayCNGiayCK_us .modal-body::-webkit-scrollbar {
            display: none;
        }

    #mdXemChiTietGCK_us {
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 !important;
        padding-right: 0px !important;
        padding: 0 !important;
        scroll-behavior: smooth;
    }

        #mdXemChiTietGCK_us .modal-content {
            min-height: 100vh !important;
        }

        #mdXemChiTietGCK_us .modal-dialog {
            width: 100% !important;
            max-width: 100% !important;
            padding: 0 !important;
            margin: unset !important;
        }

    @media (max-width: 468px) {
        #mdXemChiTietGCK_us .modal-dialog .modal-body {
            margin-bottom: 40px !important;
        }
    }

    #mdXemChiTietGCK_us .modal-body {
        max-height: 95vh; /* Thiết lập chiều cao tối đa */
        overflow-y: scroll; /* Cho phép cuộn dọc */
        scrollbar-width: none; /* Ẩn thanh cuộn trên Firefox */
        -ms-overflow-style: none; /* Ẩn thanh cuộn trên IE và Edge */
    }

        /* Ẩn thanh cuộn trên Webkit (Chrome, Safari) */
        #mdXemChiTietGCK_us .modal-body::-webkit-scrollbar {
            display: none;
        }
        .col-auto{
            border: 2px solid white;
            border-radius: 4px;
        }

    .khung-vien-tim-kiem {
        border: 1px solid var(--nts-bg-secondary);
        border-radius: 6px;
        padding-bottom: 7px;
    }
</style>

<input type="hidden" id="CapBangTotNghiepID" value="">
    @include('layouts.ModalPrint_Full')
@endsection
@push('scripts')
<script>
    window.Laravel = window.Laravel || {};
    window.Laravel.tracuuvbcc = {
        GetDSDiaBanHCTinh: "{{ route('tracuuvbcc.db.getdiabanhc_tinh') }}",
        GetDSDiaBanHC_ByIDCha: "{{ route('tracuuvbcc.db.getdiabanhc_byidcha') }}",
        getListQuyetDinh: `{{ route('dungchung.danhmuc.comboQuyetDinh') }}`,
        getListSoGoc: `{{ route('dungchung.danhmuc.comboSoGoc') }}`,
        getAllTiepNhanPhoiVBCC: `{{ route('tiepnhanphoivbcc.getAllTiepNhanPhoiVBCC') }}`,
        imgLuoi: `{{ asset('img/BangTotNghiep.png') }}`,

        getMauVanBang: "{{ route('capbangtotnghiep.getMauVanBang') }}",
        getMauVanBangCT: "{{ route('mauvanbangchungchi.GetAllCT') }}",
        getFileWordV2: "{{ route('capbangtotnghiep.getFileWordV2') }}",
    };
</script>
<script src="{{ asset('js/tracuu/tracuuvbcc.js') }}?v={{ time() }}"></script>
@endpush
