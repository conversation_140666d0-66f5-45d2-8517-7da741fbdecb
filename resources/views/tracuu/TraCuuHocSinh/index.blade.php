@extends('layouts.layouts')

@section('title', 'Tra cứu thông tin học sinh')

@section('content')
   <div class="main-content-inner">
    <div class="page-content">
        <div class="body-activity" id="Div1">
            <div id="DivMain">
                <div class="row">
                    <div class="khung-vien-tim-kiem">
                        <div class="row" style="margin-top: 10px;margin-bottom: 0px;">
                            <h1 class="text-center nts-text-backround" style="font-weight: bold; text-transform: uppercase">
                                Tra cứu thông tin học sinh
                            </h1>
                        </div>
                        <div class="row" style="margin-bottom: 16px">
                            <div class="col-md-3"></div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input style="border-right: 0;height: 32px !important; border-top-right-radius: 0 !important; border-bottom-right-radius: 0 !important;" class="form-control input-sm" type="text" id="timKiemG" placeholder="Nội dung tìm kiếm ..." autocomplete="off">
                                    <span class="input-group-addon" style="margin: 0; padding: 0; border: none;">
                                        <button class="btn btn-white btn-sm nts-secondary" title="Tìm kiếm" id="btnTimKiem" style="border-top-left-radius: 0 !important; border-bottom-left-radius: 0 !important; width: 35px; height: 32px "><span class="fas fa-search" aria-hidden="true"></span></button>
                                        <button class="btn btn-primary btn-sm nts-color-timkiem" id="TimKiemNangCao" style="border-top-left-radius: 0 !important; border-bottom-left-radius: 0 !important; width: 35px; height: 32px; margin-left: -4px;"><span class="fas fa-sliders-h" aria-hidden="true"></span></button>
                                    </span>
                                </div>
                                <div id="KhungTimKiem" style="width: 42.3%; z-index: 99; position: absolute; margin-top: 4px; background: rgb(255, 255, 255); padding: 12px 12px 8px; border: 1px solid rgb(229, 229, 229); border-radius: 4px; box-shadow: rgba(0, 0, 0, 0.2) 0px 5px 15px; display: none;">
                                    <div class="row" style=" margin-bottom: 4px">
                                        <div class="col-md-6">
                                            <span style="font-size: 16px;" class="nts-secondary"><i class="fa fa-search"></i>&ensp;Tìm kiếm nâng cao</span>
                                        </div>
                                    </div>

                                    <div class="row mb-2">
                                        <div class="col-md-12">
                                            <label class="form-label" for="DonViID_TimKiem">Trường học</label>
                                            <select class="form-control input-sm" id="DonViID_TimKiem">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row mb-2">
                                         <div class=" col-md-6">
                                            <label class="">Tỉnh/thành phố</label>
                                            <select class="form-control input-sm" id="DiaBanHCID_Tinh_TimKiem" ></select>
                                        </div>

                                         <div class=" col-md-6">
                                            <label for="" class="">Xã/phường</label>
                                            <select class="form-control input-sm" id="DiaBanHCID_Xa_TimKiem"></select>
                                        </div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-md-12">
                                            <label class="form-label" for="TrangThaiCBID_TimKiem">Trạng thái cấp bằng</label>
                                            <select class="form-control input-sm" id="TrangThaiCBID_TimKiem">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group" style="text-align: right">
                                                <a href="#" class="nts-color-dong btn btn-outline-danger" id="DongTimKiem"><i class="fa fa-close"></i>&ensp;Đóng</a>
                                                <button type="button" id="TimKiem" class="btn btn-sm btn-success nts-color-timkiem"><i class="fa fa-search"></i>&ensp;Tìm kiếm</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3"></div>
                        </div>

                        <div class="col-12" id="DivThongKe">
                            <div class="row row-cards">
                                <div class="col-sm-6 col-lg-3">
                                    <div class="card card-sm card-margin-0">
                                        <div class="card-body card-buoc-tra-cuu-s1" style="background-color: #1C6BA7">
                                            <div class="row align-items-center" style="padding-left: 10px;">
                                                <div class="col-auto">
                                                    <span class="text-white card-stt-buoc-tra-cuu">
                                                        1
                                                    </span>
                                                </div>
                                                <div class="col">
                                                    <div class="text-white card-tieu-de-buoc-tra-cuu">
                                                        NHẬP THÔNG TIN
                                                    </div>
                                                    <div class="text-white">
                                                        Nhập thông tin cần tra cứu
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-lg-3">
                                    <div class="card card-sm card-margin-0">
                                        <div class="card-body card-buoc-tra-cuu-s2" style="background-color: #1A9DBC">
                                            <div class="row align-items-center" style="padding-left: 10px;">
                                                <div class="col-auto">
                                                    <span class="text-white card-stt-buoc-tra-cuu">
                                                        2
                                                    </span>
                                                </div>
                                                <div class="col">
                                                    <div class="text-white card-tieu-de-buoc-tra-cuu">
                                                        TRA CỨU NÂNG CAO
                                                    </div>
                                                    <div class="text-white">
                                                        Nhấn <i class="fa fas fa-sliders-h" style="border: 1px solid #fff; padding: 4px; border-radius: 4px;"></i> để chọn các tham số tra cứu nâng cao (nếu có)
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-lg-3">
                                    <div class="card card-sm card-margin-0">
                                        <div class="card-body card-buoc-tra-cuu-s3" style="background-color: #EFA93C">
                                            <div class="row align-items-center" style="padding-left: 10px;">
                                                <div class="col-auto">
                                                    <span class="text-white card-stt-buoc-tra-cuu">
                                                        3
                                                    </span>
                                                </div>
                                                <div class="col">
                                                    <div class="text-white card-tieu-de-buoc-tra-cuu">
                                                        TRA CỨU
                                                    </div>
                                                    <div class="text-white">
                                                        Nhấn <i class="fa fas fa-search" style="border: 1px solid #fff; padding: 4px; border-radius: 4px;"></i> để tiến hành tra cứu
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-lg-3">
                                    <div class="card card-sm card-margin-0">
                                        <div class="card-body card-buoc-tra-cuu-s4" style="background-color: #14937E">
                                            <div class="row align-items-center" style="padding-left: 10px;">
                                                <div class="col-auto">
                                                    <span class="text-white card-stt-buoc-tra-cuu">
                                                        4
                                                    </span>
                                                </div>
                                                <div class="col">
                                                    <div class="text-white card-tieu-de-buoc-tra-cuu">
                                                        XEM KẾT QUẢ
                                                    </div>
                                                    <div class="text-white">
                                                        Xem kết quả sau khi tra cứu
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="DivTimKiem">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label" style="margin-top: 10px;font-weight: bold;">Tìm thấy <span id="lblSoLuongKetQua">...</span> kết quả tìm kiếm</label>
                    </div>
                    <div class="col-md-8">
                        <div class="text-end">
                            <div class="btn-group" style="height:40px">
                                <a id="btnAnHienTQ" style="margin-top:4px;padding:4px;cursor:pointer; color:#41d81e;" class=""><i id="iconAnHien" class="fa fa-eye-slash" aria-hidden="true"></i><span id="textAnHien"> Ẩn hướng dẫn tìm kiếm</span></a>
                            </div>
                            <span style="" class="btn position-relative">
                                <select class="form-control" id="CbSapXep" tabindex="0" style="padding: 0.05rem 0.55rem !important; cursor: pointer">
                                    <option value="Hovaten" selected="">Họ và tên</option>
                                    <option value="MaDoiTuong">Mã học sinh</option>
                                </select>
                                <span id="BtnSapXepTangGiam" style="margin-left:4px;">
                                    <i class="fa fa-sort-alpha-asc" aria-hidden="true"></i>
                                </span>
                            </span>
                            <div class="btn-group" role="group">
                                <input type="radio" class="btn-check height-button-icon" title="Xem dạng lưới" name="btn-layout" id="btn-layout-1" autocomplete="off">
                                <label style="margin-bottom: 0px" for="btn-layout-1" class="btn btn-icon">
                                    <svg class="icon icon-tabler icon-tabler-list" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M9 6l11 0"></path>
                                        <path d="M9 12l11 0"></path>
                                        <path d="M9 18l11 0"></path>
                                        <path d="M5 6l0 .01"></path>
                                        <path d="M5 12l0 .01"></path>
                                        <path d="M5 18l0 .01"></path>
                                    </svg>
                                </label>
                                <input type="radio" class="btn-check height-button-icon" title="Xem dạng danh sách" name="btn-layout" id="btn-layout-2" autocomplete="off" checked="">
                                <label style="margin-bottom: 0px" for="btn-layout-2" class="btn btn-icon">
                                    <svg class="icon icon-tabler icon-tabler-list-details" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M13 5h8"></path>
                                        <path d="M13 9h5"></path>
                                        <path d="M13 15h8"></path>
                                        <path d="M13 19h5"></path>
                                        <path d="M3 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"></path>
                                        <path d="M3 14m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"></path>
                                    </svg>
                                </label>
                                &ensp;
                                <div class="btn-group" style="">
                                    <div class="dropdown d-inline">
                                        <button class="btn nts-color-them btn-primary dropdown-toggle-hide-arrow height-button-icon" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <i class="blue fa fa-ellipsis-h"></i>
                                        </button>
                                        <div class="dropdown-menu dropdown-menu-lg-end w-auto nts-border-secondary" style="left:-110px; top: 20px">
                                            <a class="dropdown-item nts-color-in textsize-item btntienich" href="javascript:void(0);" id="btnPrint" onclick="previewExportv1('pdf',GridMainLuoi);return false;"><i class="text-warning fa fa-print iconsize-item"></i>&ensp; In</a>
                                            <a class="dropdown-item nts-color-excel textsize-item btntienich" href="javascript:void(0);" id="btnExport" onclick="previewExportv1('excel',GridMainLuoi);return false;"><i class="text-success fa fa-file-excel-o iconsize-item"></i>&ensp; Xuất Excel</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="DivDanhSach" style="margin-top: 4px">
                <div class="row">
                    <div id="GridMainDS" class=""></div>
                </div>
            </div>
            <div id="DivLuoi" style="display:none">
                <div class="row">
                    <div class="col-md-12">
                        <div id="GridMainLuoi" class="GridData"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="body-activity" id="Div1">
        {{-- <link href="/Content/GIS/leaflet.css" rel="stylesheet"> --}}

<style type="text/css">

    .card-buoc-tra-cuu-s1,
    .card-buoc-tra-cuu-s2,
    .card-buoc-tra-cuu-s3,
    .card-buoc-tra-cuu-s4 {
    min-height: 90px; /* Hoặc giá trị cao hơn nếu nội dung nhiều */
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 20px;
    border-radius: 8px;
}

.card-stt-buoc-tra-cuu {
    font-size: 24px;
    font-weight: bold;
}

.card-tieu-de-buoc-tra-cuu {
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 4px;
}

.card-margin-0 {
    margin-bottom: 16px; /* Giúp cách đều khi xuống mobile */
}

@media (min-width: 768px) {
    .card-margin-0 {
        margin-bottom: 0; /* Không cần margin nếu desktop 1 hàng */
    }
}

</style>


<style id="Modalcss">
    .modal-footer {
        flex-wrap: unset;
        padding-left: 20px;
    }

    .form-label {
        font-weight: unset;
    }

    #mdXemThongTinGiayCNGiayCK_us {
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 !important;
        padding-right: 0px !important;
        padding: 0 !important;
        scroll-behavior: smooth;
    }

        #mdXemThongTinGiayCNGiayCK_us .modal-content {
            min-height: 100vh !important;
        }

        #mdXemThongTinGiayCNGiayCK_us .modal-dialog {
            width: 100% !important;
            max-width: 100% !important;
            padding: 0 !important;
            margin: unset !important;
        }

    @media (max-width: 468px) {
        #mdXemThongTinGiayCNGiayCK_us .modal-dialog .modal-body {
            margin-bottom: 40px !important;
        }
    }

    #mdXemThongTinGiayCNGiayCK_us .modal-body {
        max-height: 95vh; /* Thiết lập chiều cao tối đa */
        overflow-y: scroll; /* Cho phép cuộn dọc */
        scrollbar-width: none; /* Ẩn thanh cuộn trên Firefox */
        -ms-overflow-style: none; /* Ẩn thanh cuộn trên IE và Edge */
    }

        /* Ẩn thanh cuộn trên Webkit (Chrome, Safari) */
        #mdXemThongTinGiayCNGiayCK_us .modal-body::-webkit-scrollbar {
            display: none;
        }

    #mdXemChiTietGCK_us {
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 !important;
        padding-right: 0px !important;
        padding: 0 !important;
        scroll-behavior: smooth;
    }

        #mdXemChiTietGCK_us .modal-content {
            min-height: 100vh !important;
        }

        #mdXemChiTietGCK_us .modal-dialog {
            width: 100% !important;
            max-width: 100% !important;
            padding: 0 !important;
            margin: unset !important;
        }

    @media (max-width: 468px) {
        #mdXemChiTietGCK_us .modal-dialog .modal-body {
            margin-bottom: 40px !important;
        }
    }

    #mdXemChiTietGCK_us .modal-body {
        max-height: 95vh; /* Thiết lập chiều cao tối đa */
        overflow-y: scroll; /* Cho phép cuộn dọc */
        scrollbar-width: none; /* Ẩn thanh cuộn trên Firefox */
        -ms-overflow-style: none; /* Ẩn thanh cuộn trên IE và Edge */
    }

        /* Ẩn thanh cuộn trên Webkit (Chrome, Safari) */
        #mdXemChiTietGCK_us .modal-body::-webkit-scrollbar {
            display: none;
        }
        .col-auto{
            border: 2px solid white;
            border-radius: 4px;
        }
</style>
    <style type="text/css">
        #KhungTimKiem .fw-bold {
            color: #f76707 !important;
        }

        .list-item {
            border-radius: 6px;
            box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.1);
        }

        #Grid1.tabulator {
            border: 1px solid #ffffff00 !important;
            background-color: #ffffff00 !important;
        }

        #Grid1.tabulator .tabulator-tableholder {
            background-color: #ffffff00 !important;
        }

        #Grid1.tabulator .tabulator-tableholder .tabulator-table {
            background-color: #ffffff00 !important;
        }

        #Grid1 .tabulator-row.tabulator-selected {
            background-color: #ffffff00 !important;
        }

        .TieuDeLabel span {
            font-weight: bold;
        }

        #Grid1 .tabulator-row {
            background-color: #ffffff00 !important;
            border: unset !important;
        }

        #Grid1 .tabulator-row>.tabulator-cell {
            border-right: 0px solid #ffffff00 !important;
            padding: 2px 4px 2px 2px !important;
        }

        #Grid1.tabulator .tabulator-footer {
            border: 1px solid #dedede !important;
            padding: 0px !important;
        }

        .tabulator-row.tabulator-selectable:hover>.tabulator-cell .show-or-hide {
            display: block !important;
            background: white;
            padding: 5px 13px;
        }
    </style>
    <style type="text/css">
        .frame-file {
            width: 65px;
            height: 65px;
            border: 1px solid #d5d5d5;
            border-radius: 4px;
            float: left;
            position: relative;
        }

        .frame-image {
            width: 60px;
            height: 60px;
            float: left;
            background-position: center;
            background-size: cover;
            border-radius: 4px;
            position: relative;
        }

        .frame-file .fa-arrow-down {
            position: absolute;
            top: -8px;
            right: 8px;
            font-size: 15px;
            color: #fff;
            background-color: #4CAF50;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: none;
            padding-top: 2px;
            text-align: center;
            z-index: 99;
        }

        .frame-file .fa-trash-o {
            position: absolute;
            top: -8px;
            right: -8px;
            font-size: 15px;
            color: #fff;
            background-color: red;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: none;
            padding-top: 2px;
            text-align: center;
            z-index: 99;
        }

        .frame-top {
            float: left;
            width: 100%;
            text-align: center;
        }

        .frame-file:hover>.fa-trash-o,
        .frame-file:hover>.fa-arrow-down {
            display: block;
        }
    </style>
    <style type="text/css">
        .span-trangthai {
            text-align: center;
            padding: 2px 10px;
            --tblr-alert-color: #ffffff00;
            --tblr-alert-bg: var(--tblr-surface);
            border: var(--tblr-border-width) var(--tblr-border-style) rgb(255 255 255 / 0%);
            border-left: .25rem var(--tblr-border-style) var(--tblr-alert-color);
            box-shadow: rgba(24, 36, 51, .04) 0 2px 4px 0;
            color: white;
        }

        /* .list-item hr {
            color: inherit;
            background-color: #f76707;
            border-top: 2px solid #f76707 !important;
            margin: 0 !important;
        } */

        .profile-picture {
            width: 100%;
            height: 130px;
            display: flex;
        }

        #mdThemMoi {
            width: 100% !important;
            max-width: 100% !important;
            margin: 0 !important;
            padding-right: 0px !important;
            padding: 0 !important;
            scroll-behavior: smooth;
        }

        #mdThemMoi .modal-content {
            min-height: 100vh !important;
        }

        #mdThemMoi .modal-dialog {
            width: 100% !important;
            max-width: 100% !important;
            padding: 0 !important;
            margin: unset !important;
        }

        #mdNhanExcel .modal-dialog {
            width: 100% !important;
            max-width: 100% !important;
        }



        @media (max-width: 468px) {
            #mdThemMoi .modal-dialog .modal-body {
                margin-bottom: 40px !important;
            }

        }

        #chonAvatar_DoiTuong {
            cursor: pointer;
        }

        html {
            scroll-behavior: smooth;
        }

        .sticky {
            position: -webkit-sticky;
            /* Safari */
            position: sticky;
            top: 0px;
        }

        .sectionThongTin {
            position: relative;
        }

        .info-container {
            background-image: url('{{ asset('img/bg1.jpg') }}');
            color: #fff;
        }

        .backdrop {
            width: 100%;
            height: 200px;
            background-size: cover;
            background-position: 50% 0;
            background-repeat: no-repeat;
            position: relative;
        }

        .backdrop:before {
            position: absolute;
            content: "";
            width: 100%;
            top: 0;
            bottom: 0;
            /* background-color: rgba(2, 13, 24, .75); */
        }

        .info-container-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            padding-top: 15px;
            position: relative;
        }

        .info-container-header-left {
            display: flex;
            align-items: center;
            gap: 0 10px;
        }

        .info-img-container {
            width: 64px;
            height: 64px;
            object-fit: cover;
            border-radius: 100%;
            border: 2px solid #ccc;
        }

        .info-img {
            width: 100%;
            height: 100%;
            border-radius: inherit;
        }

        .info-title {
            font-size: 15px;
        }

        .info-subtitle {
            font-size: 13px;
        }



        .info-body {
            margin: 0 10px !important;
            margin-top: -100px !important;
        }

        #Grid2 .tabulator-footer-contents {
            flex-direction: column !important;
            align-items: unset !important;
            text-align: left;
        }

        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: fixed;
            background-color: white;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 100;
            border-radius: 8px;
            left: 30px;
        }

        .menu-tabu {
            float: left;
            margin-left: 8px;
            padding: 5px 10px 5px 10px;
            text-decoration: none !important;
            width: 85%;
            display: block !important;
            text-align: left;
        }

        .dropdown:hover .dropdown-content {
            display: block;
        }


        .dropdown-menu-arrow:before {
            content: "";
            position: absolute;
            top: -.25rem;
            left: .75rem;
            display: block;
            background: inherit;
            width: 14px;
            height: 14px;
            transform: rotate(45deg);
            transform-origin: center;
            border: 1px solid;
            border-color: inherit;
            z-index: -1;
            clip: rect(0, 9px, 9px, 0);
            color: white;
        }

        .title-section {
            background: blue;
            width: fit-content;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            position: absolute;
            top: -15px;
        }

        .width-edit-hogiadinh {
            width: 240px;
        }

        .nav-tabs .nav-item.show .nav-link,
        .nav-tabs .nav-link.active {
            border-color: var(--tblr-card-bg);
        }

        .nav-link {
            position: relative;
        }

        .nav-tabs .nav-link:focus,
        .nav-tabs .nav-link:hover {
            border-color: white
        }

        #avatar_DoiTuong,
        #avatar {
            width: 170px;
            height: 130px;
            object-fit: cover;
        }


        @media (min-width: 1068px) {
            #mdCauhinhluoi .modal-dialog {
                width: 95% !important;
                max-width: 95% !important;
            }
        }

        @media (max-width: 992px) {
            .page-wrapper {
                margin-left: unset !important;
            }

            .tabulator-cell[tabulator-field="ThongTinHoGiaDinh"] {
                height: auto !important;
            }
        }

        /*độ rộng hình ảnh*/
        .profile-picture {
            width: 100%;
            height: 130px;
        }

        .profile-picture .img-thumbnail {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        #Grid2 .tabulator-footer-contents .tabulator-page-counter {
            text-align: right !important;
            padding-bottom: 3px;
        }

        .KhungVien {
            padding: 5px 10px 0px 10px !important;
        }

        .GridData .dropdown-item {
            padding: 5px 12px !important;
        }

        .line {
            flex-grow: 2;
            border-bottom: 1px solid #dadcde;
            margin: 0 10px;
        }

        .tabulator-cell .dropdown-menu-end {
            position: fixed !important;
        }

        .node:hover>image {
            width: 55px;
            height: 55px;
            transition: 0.3s;
            margin-left: 5px !important;
            margin-top: 5px !important;
        }

        svg.simple-diagram .node:hover {
            stroke: unset !important;
        }

        #diagram div svg {
            height: 520px !important
        }

        .btnThemNhanh:hover {
            border-color: #dadfe5 !important;
            text-decoration: none;
        }

        /*nhật ký*/
        .vertical-timeline {
            width: 100%;
            position: relative;
            padding: 1.5rem 0 1rem;
            margin-top: 1rem;
        }

        .vertical-timeline-element {
            position: relative;
            margin: 0 0 1rem;
        }

        .vertical-timeline--animate .vertical-timeline-element-icon.bounce-in {
            visibility: visible;
            animation: cd-bounce-1 .8s;
        }

        .vertical-timeline-element-icon {
            left: 10px !important;
        }

        .vertical-timeline-element-icon {
            position: absolute;
            top: 0;
            left: 60px;
        }

        .vertical-timeline-element-content {
            margin-left: 50px !important;
            padding-right: 10px !important;
        }

        .vertical-timeline-element-content {
            position: relative;
            margin-left: 90px;
            font-size: .8rem;
        }

        .vertical-timeline-element-content:after {
            content: "";
            display: table;
            clear: both;
        }

        .vertical-timeline::before {
            left: 24px !important;
        }

        .vertical-timeline-success::before {
            opacity: 1;
            background-color: rgba(47, 179, 68, 1) !important;
        }

        .vertical-timeline::before {
            content: '';
            position: absolute;
            top: 0;
            left: 67px;
            height: 100%;
            width: 4px;
            background: #e9ecef;
            border-radius: 0.25rem;
        }

        .vertical-timeline-element-icon .badge-dot-xl {
            box-shadow: 0 0 0 5px #fff;
        }

        .badge-success {
            color: #fff;
            background-color: #28a745;
        }

        .badge-dot-xl {
            width: 18px;
            height: 18px;
            position: relative;
        }

        .offcanvas {
            width: 500px !important;
        }

        .bg-transparent {
            --tblr-box-shadow-border: inset 0 0 0 1px var(--tblr-border-color-translucent);
            --tblr-bg-surface-secondary: var(--tblr-gray-100);
            --tblr-avatar-bg: var(--tblr-bg-surface-secondary);
            justify-content: center;
            color: var(--tblr-secondary);
            vertical-align: bottom;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            background: var(--tblr-avatar-bg) no-repeat center;
            border-radius: var(--tblr-border-radius);
            box-shadow: var(--tblr-avatar-box-shadow);
            padding-left: 12px;
            padding-top: 8px;
        }

        .timeline-yellow {
            border: 1px solid #f59f004b;
        }

        .timeline-green {
            border: 1px solid #2fb3444b;
        }

        .timeline-blue {
            border: 1px solid #0054a63e;
        }

        .timeline-orange {
            border: 1px solid #f7670746;
        }

        .timeline-red {
            border: 1px solid #d639394b;
        }

        .section-diachi .label-text {
            color: #018ec9;
            font-weight: bold;
        }

        #box-double-input span.select2-selection.select2-selection--single {
            border-top-right-radius: unset;
            border-bottom-right-radius: unset;
        }

        #box-double-input input#SoHoKhau {
            border-top-left-radius: unset;
            border-bottom-left-radius: unset;
        }
        #GridMainDS.tabulator,
        #GridMainDS .tabulator-tableholder,
        #GridMainDS .tabulator-row,
        #GridMainDS .tabulator .tabulator-tableholder .tabulator-table,
        #GridMainDS .tabulator-col-resize-handle,
        #GridMainDS div.tabulator-cell,
        #GridMainDS .tabulator-row .tabulator-cell,
        #GridMainDS .tabulator-row.tabulator-selectable.tabulator-row-even,
        #GridMainDS.tabulator .tabulator-tableholder .tabulator-table{
            background-color: transparent !important;
            border: none !important;
        }

        #GridMainDS .tabulator-footer{
            border: 1px solid #dbdbe1 !important;
        }
        .khung-vien-tim-kiem{
            border: 1px solid var(--nts-bg-secondary);
            border-radius: 6px;
            padding-bottom: 7px;
        }
        .card.card-sm.card-margin-0{
            border: none;
        }
    </style>

<input type="hidden" id="tracuuhocsinhID" value="">
@include('layouts.XemChiTietHocSinh')

@endsection
@push('scripts')
<script>
    window.Laravel = window.Laravel || {};
    window.Laravel.tracuuhocsinh = {
        GetDSDiaBanHCTinh: "{{ route('tracuuhocsinh.db.getdiabanhc_tinh') }}",
        GetDSDiaBanHC_ByIDCha: "{{ route('tracuuhocsinh.db.getdiabanhc_byidcha') }}",
        imgUser: `{{ asset('img/user.png') }}`,
        fileIconUrl: "{{ asset('') }}",
    };
</script>
<script src="{{ asset('js/tracuu/tracuuhocsinh.js') }}?v={{ time() }}"></script>
@endpush
