@extends('layouts.layouts')

{{-- shorthand section, no @endsection needed --}}
@section('title', 'Đơn xin cấp phôi bằng')

<style>
.tabulator-cell {
  white-space: normal !important;
  word-break: break-word;
  padding-top: 4px;
  padding-bottom: 4px;
}
</style>
 <style id="LuoiDScss">
        #GridMainDS .avatar {
            width: 60px !important;
            height: 60px !important;
            font-size: 25px !important;
            border-radius: 50%;
        }

        #GridMainDS .tabulator-tableholder .tabulator-table .tabulator-row .tabulator-cell {
            height: auto !important;
        }

        #GridMainDS .tabulator-row {
            border-bottom: 0px !important;
        }

        .row-girdmain {
            margin-bottom: 0px !important;
        }

        .tabulator-cell .dropdown-menu-end {
            position: fixed !important;
        }

        #GridMainDS .alert {
            --tblr-alert-padding-x: 1rem;
            --tblr-alert-padding-y: 0.3rem;
            --tblr-alert-margin-bottom: 0rem;
        }
     
 .apexcharts-text {
            fill: #ffffff !important;
        }

 #mdXemThongTin {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding-right: 0px !important;
    padding: 0 !important;
    scroll-behavior: smooth;
}

#mdXemThongTin.modal .modal-header {
    color: #f76d23 !important;
    border-radius: 0px !important;
}

#mdXemThongTin.modal .modal-footer {
    border-radius: 0px !important;
}

#mdXemThongTin.modal .modal-header .btn-close {
    color: #f76d23 !important;
}

#mdXemThongTin .modal-content {
    min-height: 100vh !important;
}

#mdXemThongTin .modal-dialog {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0 !important;
    margin: unset !important;
}

#mdXemThongTin .card-header-tabs .nav-link.active {
    color: #F76707 !important;
    border-bottom: 2px solid #F76707 !important;
}

  #DivThongKe .apexcharts-text {
    fill: #fff !important; 
  }
    </style>

@section('content')
    <div class="main-content divmain">
        <div id="DivThongKe" class="row" style="margin-bottom:4px">
            <div class="col-md-4 d-flex">
                <div class="card w-100">
                    <div class="card-body d-flex align-items-center justify-content-center">
                        <div id="XepLoaiChart" style="height: 100px; width: 100%;"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-8" style="padding-left:unset; display: flex;">
                <div class="card w-100">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12">
                                <h3 style="color:darkgreen" class="card-title"><i class="fa fa-th-list"
                                        aria-hidden="true"></i> Tổng số: <b><u><b id="txtTong">0</b></u> trong
                                        đó:</b></h3>
                            </div>
                        </div>
                        <hr style="margin-top:4px">
                        <div class="row">
                            <div class="col-md-6" style="border-right: 1px solid #07a607">
                                <h3 style="color: #4265B6" class="card-title">
                                    <i class="fa fa-check-square" aria-hidden="true"></i>
                                    Đã duyệt: <b><u><b id="txtDaDeNghi">0</b></u></b>
                                </h3>
                                <h3 style="color: #2FB344 " class="card-title">
                                    <i class="fa fa-clock-o" aria-hidden="true"></i>
                                    Chờ duyệt: <b><u><b id="txtChoDuyet">0</b>
                                        </u></b>
                                </h3>
                                <h3 style="color: #EA1818" class="card-title">
                                    <i class="fa fa-ban" aria-hidden="true"></i>
                                    Bị từ chối: <b><u><b id="txtBiTuChoi">0</b></u></b>
                                </h3>
                            </div>
                            <div class="col-md-6" id="LisdSoLuongPhoi">
                             
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="DivTimKiem" class="row flex-row-reverse">
            <div class="col-md-8">
                <div class="text-end">
                    <div class="btn-group" style="height:28px">
                        <a id="btnAnHienTQ" style="color: #41d81e; margin-top: 0px; padding: 4px; cursor:pointer"><i
                                id="iconAnHien" class="fa fa-eye-slash" aria-hidden="true"></i><span id="textAnHien"> Ẩn
                                trang tổng quan</span></a>
                    </div>
                    <button style="" class="btn position-relative" id="">
                        <select class="form-control" id="CbSapXep" tabindex="0"
                            style="padding: 0.05rem 0.55rem !important;">
                            <option value="NgayLap">Ngày lập</option>
                            <option value="NgayCap">Ngày cấp</option>
                        </select>&ensp;&ensp;
                        <span id="BtnSapXepTangGiam">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="icon icon-tabler icon-tabler-sort-ascending-letters" width="24" height="24"
                                viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                <path d="M15 10v-5c0 -1.38 .62 -2 2 -2s2 .62 2 2v5m0 -3h-4" />
                                <path d="M19 21h-4l4 -7h-4" />
                                <path d="M4 15l3 3l3 -3" />
                                <path d="M7 6v12" />
                            </svg>
                        </span>
                    </button>
                    <div class="btn-group" role="group">
                        <input type="radio" class="btn-check height-button-icon" name="btn-layout" id="btn-layout-1"
                            autocomplete="off" checked="">
                        <label style="margin-bottom: 0px" for="btn-layout-1" class="btn btn-icon">
                            <svg class="icon icon-tabler icon-tabler-list" width="20" height="20" viewBox="0 0 24 24"
                                stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M9 6l11 0"></path>
                                <path d="M9 12l11 0"></path>
                                <path d="M9 18l11 0"></path>
                                <path d="M5 6l0 .01"></path>
                                <path d="M5 12l0 .01"></path>
                                <path d="M5 18l0 .01"></path>
                            </svg>
                        </label>
                        <input type="radio" class="btn-check height-button-icon" name="btn-layout" id="btn-layout-2"
                            autocomplete="off">
                        <label style="margin-bottom: 0px" for="btn-layout-2" class="btn btn-icon">
                            <svg class="icon icon-tabler icon-tabler-list-details" width="20" height="20"
                                viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M13 5h8"></path>
                                <path d="M13 9h5"></path>
                                <path d="M13 15h8"></path>
                                <path d="M13 19h5"></path>
                                <path d="M3 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z">
                                </path>
                                <path d="M3 14m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z">
                                </path>
                            </svg>
                        </label>
                    </div>
                    <button type="button" class="nts-color-them btn btn-primary d-none" id="btnThemMoi"><i
                            class="fa fa-plus"></i>&ensp;Thêm mới
                        (F2)</button>
                    <div class="btn-group">
                        <div class="dropdown d-inline">
                            <button class="btn btn-primary dropdown-toggle-hide-arrow height-button-icon" type="button"
                                id="growthReportId" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="blue fa fa-ellipsis-h"></i>
                            </button>
                            <div class="dropdown-menu dropdown-menu-lg-end w-auto">
                                <a class="nts-color-in dropdown-item textsize-item" href="javascript:void(0);" id="btnPrint"
                                    onclick="previewExportv1('pdf',GridMainLuoi);return false;"><i
                                        class="text-warning fa fa-print iconsize-item"></i>&ensp; In</a>
                                <a class="nts-color-excel dropdown-item textsize-item" href="javascript:void(0);"
                                    id="btnExport" onclick="previewExportv1('excel',GridMainLuoi');return false;"><i
                                        class="text-success fa fa-file-excel-o iconsize-item"></i>&ensp; Xuất
                                    Excel</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div style="display: flex; align-items: center" class="DivTimKiem">
                    <div class="input-icon">
                        <input type="text" value="" class="form-control" placeholder="Nội dung tìm kiếm ..." id="SearchKey"
                            autocomplete="off" style="border-top-right-radius: 0px; border-bottom-right-radius: 0px; ">
                        <span class="input-icon-addon">
                            <i class="fa fa-search"></i>
                        </span>
                    </div>
                    <button id="TimKiemNangCao" class="btn btn-primary height-button-icon" type="button"
                        style="border-top-left-radius: 0px;border-bottom-left-radius:0px;">
                        <span class="fa fa-sliders" aria-hidden="true"> </span>
                    </button>
                </div>
                <div id="KhungTimKiem" class="bg-white mt-1 px-4 py-2 border rounded shadow position-absolute"
                    style="z-index: 99; width:380px ; display:none">
                    <div class="row">
                        <div class="col text-primary fw-bold">
                            <i class="fa fa-search"></i> Tìm kiếm nâng cao
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-lg-6">
                            <div class="mb-3">
                                <label class="form-label" for="TuNgay_Loc">Từ ngày</label>
                                <input type="text" class="form-control date-picker" id="TuNgay_Loc"
                                    autocomplete="off" data-date-format="dd/mm/yyyy">
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="mb-3">
                                <label class="form-label" for="DenNgay_Loc">Đến ngày</label>
                                <input type="text" class="form-control date-picker" id="DenNgay_Loc"
                                    autocomplete="off" data-date-format="dd/mm/yyyy">
                            </div>
                        </div>
                    </div>
                    <div class="row mb-3">
                      
                        <div class="col-md-12">
                            <label class="form-label" for="LoaiPhoiID_Loc">Loại phôi</label>
                            <select class="form-control input-sm" id="LoaiPhoiID_Loc" tabindex="0">
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label" for="TrangThaiXuLyID_Loc">Trạng thái</label>
                            <select class="form-control input-sm" id="TrangThaiXuLyID_Loc" tabindex="0">
                                    <option value="">-Tất cả-</option>
                                    <option value="51">Chờ duyệt</option>
                                    <option value="52">Thu hồi gửi</option>
                                    <option value="32">Đã phê duyệt</option>
                                    <option value="33">Thu hồi phê duyệt</option>
                                    <option value="42">Bị từ chối</option>
                            </select>
                        </div>
                    </div>
                    <hr style="margin:4px" />
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group" style="text-align:right">
                                <button type="button" id="DongTimKiem" class="btn btn-outline-danger"><i
                                        class="fa fa-times"></i>&ensp;Đóng</button>&ensp;
                                <button type="button" id="TimKiem" class="btn nts-color-them ms-auto"><i
                                        class="fa fa-search"></i>&ensp;Tìm kiếm</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row" style="margin-top: 4px" id="grid-layout">
            <input type="hidden" id="DonYeuCauID" />
            <input type="hidden" id="DonYeuCauID_View" />
            <div class="col-md-12">
                <div id="Grid1"></div>
            </div>
        </div>
        <div class="row" id="list-layout" style="margin-top: 4px;display: none;">
           <div class="row">
                <div class="col-md-12">
                    <div id="GridMainLuoi" class="GridData">

                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- Create Modal --}}
    <div class="modal modal-blur fade" id="mdThemMoi" tabindex="-1" aria-modal="true" role="dialog"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-xl" style="width: 55%;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tieuDeModal"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12" style="padding-left: 10px">
                            <fieldset class="KhungVien">
                                <legend>Thông tin đơn xin cấp bản sao văn bằng, chứng chỉ</legend>
                                <div class="row">
                                    <div class="col-lg-3">
                                        <div class="mb-3">
                                            <label class="form-label" for="SoDonXinCapBanSao">Số</label>
                                             <input type="text" class="form-control" id="SoDonXinCapBanSao" autocomplete="off" >
                                        </div>
                                    </div>
                                    <div class="col-lg-3">
                                        <div class="mb-3">
                                            <label class="form-label" for="NgayLap">Ngày lập</label>
                                            <input type="text" class="form-control date-picker" id="NgayLap"
                                                autocomplete="off" data-date-format="dd/mm/yyyy">
                                        </div>
                                    </div>
                                    <div class="col-lg-3">
                                        <div class="mb-3">
                                            <label class="form-label" for="NhanVienID_NguoiLap">Người đề nghị</label>
                                            <select class="form-control" id="NhanVienID_NguoiLap" tabindex="0">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-3">
                                        <div class="mb-3">
                                            <label class="form-label" for="ChucVuID_NguoiLap">Chức vụ</label>
                                            <select class="form-control" id="ChucVuID_NguoiLap" tabindex="0">
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="mb-3">
                                      
                                            <label class="form-label" for="HocSinhID">Học sinh</label>
                                                  <input type="hidden" id="HocSinhID" required />
                                            <div class="form-group" style="width: 100%;">
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <div class="form-group">
                                                            <div class="d-flex align-items-center">
                                                                <input class="form-control input-sm"
                                                                    style="border-top-right-radius: 0px !important;
                                                                    border-bottom-right-radius: 0px !important;"
                                                                    id="TenTruongHoc" disabled></input>
                                                                <button class="btn btn-sm btn-primary nts-color-luu"
                                                                    id="btnChonDonViCha"
                                                                    style="border-top-left-radius: 0 !important; border-bottom-left-radius: 0 !important; height: 30px;">
                                                                    <i class="blue fa fa-ellipsis-h"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                         
                                <div class="row">
                                      <div class="col-lg-3">
                                        <div class="mb-3">
                                            <label class="form-label" for="LoaiVanBangChungChiID">Loại văn bằng</label>
                                            <select class="form-control" id="LoaiVanBangChungChiID" tabindex="0" >
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-3">
                                        <div class="mb-3">
                                            <label class="form-label" for="NamTotNghiep">Năm tốt nghiệp</label>
                                            <select class="form-control" id="NamTotNghiep" tabindex="0" required>
                                            </select>
                                        </div>
                                    </div>
                                      <div class="col-lg-3">
                                        <div class="mb-3">
                                            <label class="form-label" for="XepLoai">Xếp loại</label>
                                            <select class="form-control" id="XepLoai" tabindex="0" required>
                                            </select>
                                        </div>
                                    </div>
                                      <div class="col-lg-3">
                                        <div class="mb-3">
                                            <label class="form-label" for="HinhThucDaoTaoID">Hình thức đào tạo</label>
                                            <select class="form-control" id="HinhThucDaoTaoID" tabindex="0" required>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-3">
                                        <div class="mb-3">
                                            <label class="form-label" for="SoHieu">Số hiệu</label>
                                             <input type="text" class="form-control" id="SoHieu" autocomplete="off" >
                                        </div>
                                    </div>
                                      <div class="col-lg-3">
                                        <div class="mb-3">
                                            <label class="form-label" for="NgayCap">Ngày cấp</label>
                                            <input type="text" class="form-control date-picker" id="NgayCap" autocomplete="off" data-date-format="dd/mm/yyyy">
                                        </div>
                                    </div>
                                      <div class="col-lg-6">
                                        <div class="mb-3">
                                            <label class="form-label" for="SoVaoSo">Số vào sổ</label>
                                            <input type="text" class="form-control" id="SoVaoSo" autocomplete="off" >
                                        </div>
                                    </div>
                                </div>
                                 <div class="row">
                                    <div class="col-lg-12">
                                        <div class="mb-1">
                                            <label class="form-label" for="LyDo">Lý do</label>
                                            <textarea class="form-control" id="LyDo" rows="4"></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="mb-1">
                                            <label class="form-label" for="GhiChu">Ghi chú</label>
                                            <textarea class="form-control" id="GhiChu" rows="4"></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-bottom:6px">
                                    <div class="col-md-12">
                                        @include('partials.dinh-kem', [
                                            'id' => 'dinhKemQD',
                                        ])
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div style="display: contents; align-items: center; justify-content: space-between">

                        <div class="col-md-6">
                            <div style="float:right;text-align: right">
                                <a href="#" class="nts-color-dong btn btn-outline-danger" data-bs-dismiss="modal">
                                    <i class="fa fa-close"></i>&nbsp;Đóng
                                </a>
                                <a href="#" id="btnLuuVaDong" class="nts-color-luu btn btn-success ms-auto">
                                    <i class="fa fa-save"></i>&ensp;Lưu và đóng
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- Create Modal --}}
    <div class="modal fade" id="mdGuiDonXinCapPhoiBang" tabindex="-1" aria-modal="true" role="dialog"
    data-bs-backdrop="static">
        <div class="modal-dialog modal-xl modal-dialog-centered" role="document" style=" width: 52%; ">
            <div class="modal-content p-0 rounded-3 overflow-hidden">
                <!-- Modal Header -->
                <div class="modal-header py-2 px-3" style="background:#fd7e14;color:#fff;">
                    <h5 class="modal-title fw-bold text-uppercase" id="tieuDe_mdGuiDonXinCapPhoiBang">Gửi đơn xin cấp phôi bằng
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <!-- Modal Body -->
                <div class="modal-body px-4 py-3">
                    <div class="row">
                        <!-- Illustration -->
                        <div class="col-md-5 d-flex align-items-center justify-content-center mb-2 mb-md-0">
                            <img src="/img/Logo-note2.jpeg" alt="Quyết định" class="img-fluid" style="max-width:350px;">
                        </div>
                        <!-- Right Side: Content -->
                        <div class="col-md-7">
                            <!-- Alert/Instruction -->
                            <div class="border border-warning rounded mb-3 px-3 py-2"
                                style="background:#fff7e6;font-size:15px;">
                                <span id="alertMessage">
                                    Bạn đang thực hiện gửi đơn xin cấp phôi bằng cho<b id="TenDonViNhan"></b>
                                </span>
                            </div>
                            <!-- Fieldset -->
                            <fieldset class="KhungVien border rounded-3 p-3 mb-1">
                                <legend class="float-none w" id="fieldsetLegend">
                                    Thông tin gửi đơn
                                </legend>
                                <form>
                                    <div class="row g-2">
                                        <div class="col-12 col-sm-6">
                                            <label class="form-label mb-1" for="NgayXuLy" style="" id="tieuDe_mdGuiDonXinCapPhoiBang">Ngày gửi
                                                <span class="text-danger">(*)</span></label>
                                            <div class="input-group input-group-sm">
                                                <input type="text" class="form-control date-picker" id="NgayXuLy"
                                                    name="NgayKy" autocomplete="off" data-date-format="dd/mm/yyyy"
                                                    placeholder="dd/MM/yyyy" required>

                                            </div>
                                        </div>
                                        <div class="col-12 col-sm-6"></div>
                                        <div class="col-12 col-sm-6">
                                            <label class="form-label  mb-1" for="NguoiXuLyID" style="">Người gửi</label>
                                            <select class="form-select" id="NguoiXuLyID">
                                                <option>-Chọn-</option>
                                            </select>
                                        </div>
                                        <div class="col-12 col-sm-6">
                                            <label class="form-label  mb-1" for="ChucVuNguoiXuLyID" style="">Chức
                                                vụ</label>
                                            <select class="form-select" id="ChucVuNguoiXuLyID">
                                                <option>-Chọn-</option>
                                            </select>
                                        </div>
                                        <div class="col-12">
                                            <label class="form-label mb-1" for="NoiDungXuLy" style="">Nội dung gửi</label>
                                            <div id="NoiDungXuLy" class="form-control" contenteditable="true"
                                                style=" min-height: 120px;"></div>
                                        </div>
                                    </div>
                                </form>
                            </fieldset>
                        </div>
                    </div>
                </div>
                <div class="modal-footer justify-content-end py-2 px-3" style="background:#f4f4f4;">
                    <button type="button" class="btn btn-outline-danger fw-bold px-3 me-2" data-bs-dismiss="modal">
                        <i class="fa fa-times"></i>&nbsp; Đóng
                    </button>
                    <button type="button" class="btn btn-warning text-white fw-bold px-3" id="btnGuiDon">
                        <i class="fa fa-check"></i>&nbsp; Gửi
                    </button>
                </div>
            </div>
        </div>
    </div>





 <div class="modal fade" id="mdChonHocSinh" tabindex="-1" aria-modal="true" role="dialog" data-bs-backdrop="static">
            <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
                <div class="modal-content p-0 rounded-3 overflow-hidden">
                    <!-- Header -->
                    <div class="modal-header py-2 px-3" style="background:#fd7e14;color:#fff;">
                        <h5 class="modal-title fw-bold text-uppercase" style="font-size:1.05rem;">Chọn học sinh</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                    </div>
                    <!-- Body -->
                    <div class="modal-body py-2 px-3">
                        <div class="row g-2">
                            <!-- Left: Danh sách trường học -->
                            <div class="col-12 col-md-4">
                                <fieldset class="KhungVien border rounded-3 h-100 p-0">
                                    <legend>Danh sách trường học</legend>
                                    <!-- Search -->
                                    <div class="input-group input-group-sm my-2 px-2">
                                        <div class="input-icon">
                                            <input type="text" class="form-control" placeholder="Nội dung tìm kiếm …"
                                                id="timKiemTruongHoc" autocomplete="off">
                                            <span class="input-icon-addon"><i class="fa fa-search"></i></span>
                                        </div>
                                    </div>
                                    <!-- School list (replace with Tabulator or custom HTML as needed) -->
                                    <div class="px-2 pb-2" style="min-height:360px;max-height:420px;overflow:auto;">
                                        <div id="gridTruongHoc"></div>
                                    </div>
                                </fieldset>
                            </div>
                            <!-- Right: Danh sách học sinh -->
                            <div class="col-12 col-md-8">
                                <fieldset class="KhungVien border rounded-3 h-100 p-0">
                                    <legend>Danh sách học sinh</legend>

                                    <!-- Search -->
                                    <div class="input-group input-group-sm my-2 px-2" style="max-width:350px;">
                                        <div class="input-icon">
                                            <input type="text" class="form-control" placeholder="Nội dung tìm kiếm …"
                                                id="timKiemHocSinh" autocomplete="off">
                                            <span class="input-icon-addon"><i class="fa fa-search"></i></span>
                                        </div>
                                    </div>
                                    <!-- Student grid placeholder -->
                                    <div id="tabGridHocSinhChon" class="px-2"
                                        style="min-height:360px;max-height:420px;overflow:auto;border-radius:6px;">
                                        <div id="gridChonHocSinh">

                                        </div>
                                    </div>
                                </fieldset>
                            </div>
                        </div>
                    </div>
                    <!-- Footer -->
                    <div class="modal-footer justify-content-end py-2 px-3" style="background:#f4f4f4;">
                        <button type="button" class="btn btn-outline-danger fw-bold px-3 me-2" data-bs-dismiss="modal">
                            <i class="fa fa-times"></i>&nbsp; Đóng
                        </button>
                        <button type="button" class="btn btn-warning text-white fw-bold px-3" id="btnChonVaDong">
                            <i class="fa fa-check"></i>&nbsp; Chọn và đóng
                        </button>
                    </div>
                </div>
            </div>
        </div>
<div class="modal fade" id="mdQPheDuyet" tabindex="-1" aria-modal="true" role="dialog"
    data-bs-backdrop="static">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document" style=" width: 52%; ">
        <div class="modal-content p-0 rounded-3 overflow-hidden">
            <!-- Modal Header -->
            <div class="modal-header py-2 px-3" style="background:#fd7e14;color:#fff;">
                <h5 class="modal-title fw-bold text-uppercase" id="tieuDe_mdQPheDuyet">Phê duyệt phiếu đề nghị cấp bản sao văn bằng, chứng chỉ
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <!-- Modal Body -->
            <div class="modal-body px-4 py-3">
                <div class="row">
                    <!-- Illustration -->
                    <div class="col-md-5 d-flex align-items-center justify-content-center mb-2 mb-md-0">
                        <img src="/img/Logo-note2.jpeg" alt="Quyết định" class="img-fluid" style="max-width:350px;">
                    </div>
                    <!-- Right Side: Content -->
                    <div class="col-md-7">
                        <!-- Alert/Instruction -->
                        <div class="border border-warning rounded mb-3 px-3 py-2"
                            style="background:#fff7e6;font-size:15px;">
                            <span id="alertMessage">
                                Bạn đang thực hiện phê duyệt phiếu đề nghị cấp bản sao văn bằng, chứng chỉ số: <b id="SoDeNghi"></b> ngày lập <b id="NgayDeNghi"></b> của <b id="DonViDeNghi"></b>. 
                                Vui lòng điền đầy đủ thông tin bên dưới và nhấn vào nút <b>"Gửi"</b> để thực hiện thao tác gửi phiếu đề nghị đến cơ quan có thẩm quyền.
                            </span>
                        </div>
                        <!-- Fieldset -->
                        <fieldset class="KhungVien border rounded-3 p-3 mb-1">
                            <legend class="float-none w" id="fieldsetLegend">
                                Thông tin phê duyệt
                            </legend>
                            <form>
                                <div class="row g-2">
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label mb-1" for="NgayPheDuyet" style="">Ngày phê duyệt
                                        <span class="text-danger">(*)</span></label>
                                        <div class="input-group input-group-sm">
                                            <input type="text" class="form-control date-picker" id="NgayPheDuyet" name="NgayKy" autocomplete="off" data-date-format="dd/mm/yyyy" placeholder="dd/MM/yyyy" required>
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6"></div>
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label  mb-1" for="NhanVienID_PheDuyet" style="">Người phê duyệt</label>
                                        <select class="form-select" id="NhanVienID_PheDuyet">
                                        </select>
                                    </div>
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label  mb-1" for="ChucVuID_PheDuyet" style="">Chức vụ</label>
                                        <select class="form-select" id="ChucVuID_PheDuyet">
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label mb-1" for="NoiDung_PheDuyet" style="">Nội dung</label>
                                        <div id="NoiDung_PheDuyet" class="form-control" contenteditable="true" style=" min-height: 120px;"></div>
                                    </div>
                                </div>
                            </form>
                        </fieldset>
                    </div>
                </div>
            </div>
            <!-- Modal Footer -->
            <div class="modal-footer justify-content-end py-2 px-3" style="background:#f4f4f4;">
                <button type="button" class="btn btn-outline-danger fw-bold px-3 me-2" data-bs-dismiss="modal">
                    <i class="fa fa-times"></i>&nbsp; Đóng (F4)
                </button>
                <button type="button" class="btn btn-warning text-white fw-bold px-3" id="btnBanHanh">
                    <i class="fa fa-check"></i>&nbsp; Phê duyệt (F9)
                </button>
            </div>
        </div>
    </div>
</div>





<div class="modal fade" id="mdQThuHoi" tabindex="-1" aria-modal="true" role="dialog"data-bs-backdrop="static">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document" style=" width: 52%; ">
        <div class="modal-content p-0 rounded-3 overflow-hidden">
            <!-- Modal Header -->
            <div class="modal-header py-2 px-3" style="background:#fd7e14;color:#fff;">
                <h5 class="modal-title fw-bold text-uppercase" id="tieuDe_mdQPheDuyet">Thu hồi phiếu đề nghị cấp bản sao văn bằng, chứng chỉ
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <!-- Modal Body -->
            <div class="modal-body px-4 py-3">
                <div class="row">
                    <!-- Illustration -->
                    <div class="col-md-5 d-flex align-items-center justify-content-center mb-2 mb-md-0">
                        <img src="/img/Logo-note2.jpeg" alt="Quyết định" class="img-fluid" style="max-width:350px;">
                    </div>
                    <!-- Right Side: Content -->
                    <div class="col-md-7">
                        <!-- Alert/Instruction -->
                        <div class="border border-warning rounded mb-3 px-3 py-2"
                            style="background:#fff7e6;font-size:15px;">
                            <span id="alertMessage">
                                Bạn đang thực hiện thu hồi phiếu đề nghị cấp bản sao văn bằng, chứng chỉ số: <b id="SoDeNghiTH"></b> ngày lập <b id="NgayDeNghiTH"></b> của <b id="DonViDeNghiTH"></b>. 
                                Vui lòng điền đầy đủ thông tin bên dưới và nhấn vào nút <b>"Gửi"</b> để thực hiện thao tác gửi phiếu đề nghị đến cơ quan có thẩm quyền.
                            </span>
                        </div>
                        <!-- Fieldset -->
                        <fieldset class="KhungVien border rounded-3 p-3 mb-1">
                            <legend class="float-none w" id="fieldsetLegend">
                                Thông tin thu hồi
                            </legend>
                            <form>
                                <div class="row g-2">
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label mb-1" for="NgayThuHoi" style="">Ngày thu hồi
                                            <span class="text-danger">(*)</span></label>
                                        <div class="input-group input-group-sm">
                                            <input type="text" class="form-control date-picker" id="NgayThuHoi"
                                                name="NgayKy" autocomplete="off" data-date-format="dd/mm/yyyy"
                                                placeholder="dd/MM/yyyy" required>
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6"></div>
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label  mb-1" for="NhanVienID_ThuHoi" style="">Người thu hồi</label>
                                        <select class="form-select" id="NhanVienID_ThuHoi" menu-width="150%">
                                        </select>
                                    </div>
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label  mb-1" for="ChucVuID_ThuHoi" style="">Chức vụ</label>
                                        <select class="form-select" id="ChucVuID_ThuHoi">
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label mb-1" for="NoiDung_ThuHoi" style="">Nội dung</label>
                                        <div id="NoiDung_ThuHoi" class="form-control" contenteditable="true" style=" min-height: 120px;"></div>
                                    </div>
                                </div>
                            </form>
                        </fieldset>
                    </div>
                </div>
            </div>
            <!-- Modal Footer -->
            <div class="modal-footer justify-content-end py-2 px-3" style="background:#f4f4f4;">
                <button type="button" class="btn btn-outline-danger fw-bold px-3 me-2" data-bs-dismiss="modal">
                    <i class="fa fa-times"></i>&nbsp; Đóng (F4)
                </button>
                <button type="button" class="btn btn-warning text-white fw-bold px-3" id="btnThuHoi">
                    <i class="fa fa-check"></i>&nbsp; Thu hồi (F9)
                </button>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="mdQTuChoi" tabindex="-1" aria-modal="true" role="dialog"data-bs-backdrop="static">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document" style=" width: 52%; ">
        <div class="modal-content p-0 rounded-3 overflow-hidden">
            <!-- Modal Header -->
            <div class="modal-header py-2 px-3" style="background:#fd7e14;color:#fff;">
                <h5 class="modal-title fw-bold text-uppercase" id="tieuDe_mdQPheDuyet">Từ chối phê duyệt phiếu đề nghị cấp bản sao văn bằng, chứng chỉ
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <!-- Modal Body -->
            <div class="modal-body px-4 py-3">
                <div class="row">
                    <!-- Illustration -->
                    <div class="col-md-5 d-flex align-items-center justify-content-center mb-2 mb-md-0">
                        <img src="/img/Logo-note2.jpeg" alt="Quyết định" class="img-fluid" style="max-width:350px;">
                    </div>
                    <!-- Right Side: Content -->
                    <div class="col-md-7">
                        <!-- Alert/Instruction -->
                        <div class="border border-warning rounded mb-3 px-3 py-2"
                            style="background:#fff7e6;font-size:15px;">
                            <span id="alertMessage">
                                Bạn đang thực hiện từ chối phê duyệt phiếu đề nghị cấp bản sao văn bằng, chứng chỉ số: <b id="SoDeNghiTC"></b> ngày lập <b id="NgayDeNghiTC"></b> của <b id="DonViDeNghiTC"></b>. 
                                Vui lòng điền đầy đủ thông tin bên dưới và nhấn vào nút <b>"Gửi"</b> để thực hiện thao tác gửi phiếu đề nghị đến cơ quan có thẩm quyền.
                            </span>
                        </div>
                        <!-- Fieldset -->
                        <fieldset class="KhungVien border rounded-3 p-3 mb-1">
                            <legend class="float-none w" id="fieldsetLegend">
                                Thông tin từ chối
                            </legend>
                            <form>
                                <div class="row g-2">
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label mb-1" for="NgayTuChoi" style="">Ngày từ chối
                                            <span class="text-danger">(*)</span></label>
                                        <div class="input-group input-group-sm">
                                            <input type="text" class="form-control date-picker" id="NgayTuChoi"
                                                name="NgayKy" autocomplete="off" data-date-format="dd/mm/yyyy"
                                                placeholder="dd/MM/yyyy" required>
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6"></div>
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label  mb-1" for="NhanVienID_TuChoi" style="">Người từ chối</label>
                                        <select class="form-select" id="NhanVienID_TuChoi" menu-width="150%">
                                        </select>
                                    </div>
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label  mb-1" for="ChucVuID_TuChoi" style="">Chức vụ</label>
                                        <select class="form-select" id="ChucVuID_TuChoi">
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label mb-1" for="NoiDung_TuChoi" style="">Nội dung</label>
                                        <div id="NoiDung_TuChoi" class="form-control" contenteditable="true" style=" min-height: 120px;"></div>
                                    </div>
                                </div>
                            </form>
                        </fieldset>
                    </div>
                </div>
            </div>
            <!-- Modal Footer -->
            <div class="modal-footer justify-content-end py-2 px-3" style="background:#f4f4f4;">
                <button type="button" class="btn btn-outline-danger fw-bold px-3 me-2" data-bs-dismiss="modal">
                    <i class="fa fa-times"></i>&nbsp; Đóng
                </button>
                <button type="button" class="btn btn-warning text-white fw-bold px-3" id="btnTuChoi">
                    <i class="fa fa-check"></i>&nbsp; Từ chối
                </button>
            </div>
        </div>
    </div>
</div>


<div class="modal modal-blur fade" id="mdXemThongTin"  tabindex="-1" aria-modal="true" role="dialog"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tieuDeModalXemChiTietHocSinh_us" style=" color: #ffffff; ">Nhật ký thao tác </h5>
                     <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                    <div class="col-sm-12">
                       <div class="card">
                            <div class="card-header" style=" margin-left: 11px; ">
                                <ul class="nav nav-tabs" id="detailTabsChiTiet" role="tablist" style=" margin-bottom: -17px; ">
                                    <li class="nav-item" role="presentation">
                                        <button style=" color: #fd7e14; " class="nav-link active" id="detail-ds-tab-chi-tiet" data-bs-toggle="tab" data-bs-target="#detail-ds-chi-tiet" type="button" role="tab" aria-controls="detail-ds-chi-tiet" aria-selected="true">
                                            <i class="fa fa-users"></i>&nbsp;Thông tin đơn yêu cầu
                                        </button>
                                    </li>

                                    <li class="nav-item" role="presentation" >
                                        <button style=" color: #fd7e14; "  class="nav-link" id="detail-lichsu-tab-chi-tiet" data-bs-toggle="tab" data-bs-target="#detail-lichsu-chi-tiet" type="button" role="tab" aria-controls="detail-lichsu-chi-tiet" aria-selected="false" onclick="getNhatKyThaoTac('NhatKy_XemChiTiet','don_xin_cap_ban_saos', selectedId);" tabindex="-1">
                                            <i class="fa fa-clock-o"></i>&nbsp;Lịch sử thao tác
                                        </button>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body">
                                <div class="tab-content">
                                    <div class="tab-pane active show" id="detail-ds-chi-tiet" role="tabpanel" aria-labelledby="detail-ds-tab-chi-tiet">
                                        <b style=" color: #fd7e14; ">THÔNG TIN ĐƠN VỊ YÊU CẦU</b>
                                        <hr style="border-top: 2px solid #f76707; margin: 0 0 0.5rem 0; margin-top:0.5rem;">
                                        <div class="row">
                                            <div class="col-sm-3" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Số phiếu: <b id="txtSoPhieu"></b></p>
                                            </div>
                                             <div class="col-sm-3" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Ngày lập: <b id="txtNgayLap"></b></p>
                                            </div>
                                             <div class="col-sm-3" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Người đề nghị: <b id="txtNguoiDeNghi"></b></p>
                                            </div>
                                             <div class="col-sm-3" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Chức vụ: <b id="txtChuVuNguoiDeNghi"></b></p>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Đơn vị đề nghị: <b id="txtDonViDeNghi"></b></p>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-3" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Học sinh: <b id="txtHocSinh"></b></p>
                                            </div>
                                             <div class="col-sm-3" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Năm tốt nghiệp: <b id="txtNamTotNghiep"></b></p>
                                            </div>
                                             <div class="col-sm-3" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Loại văn bằng: <b id="txtLoaiVanBang"></b></p>
                                            </div>
                                            <div class="col-sm-3" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Số hiệu: <b id="txtSoHieu"></b></p>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-3" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Ngày cấp: <b id="txtNgayCap"></b></p>
                                            </div>
                                             <div class="col-sm-9" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Lý do cấp: <b id="txtLyDo"></b></p>
                                            </div>
                                            
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12" id="txtDinhKem" style=" padding: 4px; ">
                                                
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12" id="txtTrangThai" style=" padding: 4px; ">
                                             
                                            </div>
                                        </div>  
                                        <div class="row" id="rowThongTinGui">
                                            <div class="col-sm-3" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Ngày gửi: <b id="txtNgayGui"></b></p>
                                            </div>
                                             <div class="col-sm-3" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Người gửi: <b id="txtNguoiGui"></b></p>
                                            </div>
                                             <div class="col-sm-3" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Chức vụ: <b id="txtChucVuGui"></b></p>
                                            </div>
                                            <div class="col-sm-12" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Nội dung: <b id="txtNoiDungGui"></b></p>
                                            </div>
                                        </div>
                                         <b style=" color: #fd7e14; ">THÔNG TIN ĐƠN VỊ TIẾP NHẬN XỬ LÝ</b>
                                        <hr style="border-top: 2px solid #f76707; margin: 0 0 0.5rem 0; margin-top:0.5rem;">
                                        <div class="row" id="rowThongTinPheDuyet" style="display: none">
                                            <div class="col-sm-3" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Ngày phê duyệt: <b id="txtNgayPheDuyet"></b></p>
                                            </div>
                                             <div class="col-sm-3" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Người phê duyệt: <b id="txtNguoiPheDuyet"></b></p>
                                            </div>
                                             <div class="col-sm-3" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Chức vụ: <b id="txtChucVuPheDuyet"></b></p>
                                            </div>
                                            <div class="col-sm-12" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Nội dung: <b id="txtNoiDungPheDuyet"></b></p>
                                            </div>
                                        </div>  
                                        <div class="row" id="rowTuChoi" style="display: none">
                                            <div class="col-sm-3" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Ngày từ chối: <b id="txtNgayTuChoi"></b></p>
                                            </div>
                                             <div class="col-sm-3" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Người từ chối: <b id="txtNguoiTuChoi"></b></p>
                                            </div>
                                             <div class="col-sm-3" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Chức vụ: <b id="txtChucVuTuChoi"></b></p>
                                            </div>
                                            <div class="col-sm-12" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Nội dung: <b id="txtNoiDungTuChoi"></b></p>
                                            </div>
                                        </div>  
                                        <div class="row" id="rowThuHoi" style="display: none">
                                            <div class="col-sm-3" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Ngày thu hồi: <b id="txtNgayThuHoi"></b></p>
                                            </div>
                                             <div class="col-sm-3" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Người thu hồi: <b id="txtNguoiThuHoi"></b></p>
                                            </div>
                                             <div class="col-sm-3" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Chức vụ: <b id="txtChucVuThuHoi"></b></p>
                                            </div>
                                             <div class="col-sm-12" style=" padding: 4px; ">
                                                  <p class="fs-big my-1">Nội dung: <b id="txtNoiDungThuHoi"></b></p>
                                            </div>
                                        </div>
                                    </div>
                                        <div class="tab-pane fade" id="detail-lichsu-chi-tiet" role="tabpanel" aria-labelledby="detail-lichsu-tab-chi-tiet">
                                            <div class="p-3 ">
                                                <div id="NhatKy_XemChiTiet">
                                                    @include('partials.nhatkythaotac', ['containerId' => 'NhatKy_XemChiTiet'])
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                    </div>
                    
                </div>
                    </div>
                </div>
                
            </div>
        </div>



    <input type="hidden" id="DonXinCapBanSaoID"/>

    @include('layouts.ChonDonVi')
    @include('layouts.XemChiTietHocSinh')
@endsection
@push('scripts')
    <script>
        window.Laravel = window.Laravel || {}; // 👈 Bảo vệ trước khi gán
        window.Laravel.local = {
            getListChucVu: "{{ route('dungchung.danhmuc.comboChucVu') }}",
            getListCapHoc: `{{{ route('dungchung.danhmuc.comboCapHoc') }}}`,
            comboNhanVien: "{{ route('dungchung.danhmuc.comboNhanVien') }}",
            getListDonVi: `{{{ route('dungchung.danhmuc.comboDonVi') }}}`,
            getloaiphoivanbangchungchi: "{{ route('loaiphoivanbangchungchi.getloaiphoivanbangchungchi') }}",
            getListTrangThaiCapBang: "{{ route('donxincapphoibang.getListTrangThaiCapBang') }}",
            getListXepLoai: "{{ route('dungchung.danhmuc.comboXepLoai') }}",
             getListHTDT: `{{{ route('dungchung.danhmuc.comboHTDT') }}}`,
            linkAnhDonXin: "{{asset('img/DonXinCapPhoiBang.png')}}",
            getThongKe: "{{ route('duyetdonxincapbansao.getAllthongkevanbangdasudung') }}",
             getAllLoaiVanBangCC: "{{ route('duyetdonxincapbansao.getAllLoaiVanBangCC') }}",
            getListHS: function(donviId_Truong) {
                    return "{{ route('quanly.dungchung.getHSByTruongHoc', ['donviId_TruongHoc' => 'DONVI_ID_PLACEHOLDER']) }}"
                        .replace('DONVI_ID_PLACEHOLDER', donviId_Truong );
            },
           getListTruongHoc: "{{ route('quanly.dungchung.getDanhSachTruongHoc') }}",
        };
        // pre-generate the exact URL you want to call
    </script>
    <script src="{{ asset('js/quanly/DuyetDonXinCapBanSao.js') }}?v={{ time() }}"></script>
@endpush