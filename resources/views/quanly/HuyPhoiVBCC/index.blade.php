@extends('layouts.layouts')

@section('content')
    <style id="DinhKemcss">

    </style>
    <style id="LuoiDScss">
        #GridMainDS .avatar {
            width: 60px !important;
            height: 60px !important;
            font-size: 25px !important;
            border-radius: 50%;
        }

        #GridMainDS .tabulator-tableholder .tabulator-table .tabulator-row .tabulator-cell {
            height: auto !important;
        }

        #GridMainDS .tabulator-row {
            border-bottom: 0px !important;
        }

        .row-girdmain {
            margin-bottom: 0px !important;
        }

        .tabulator-cell .dropdown-menu-end {
            position: fixed !important;
        }

        #GridMainDS .alert {
            --tblr-alert-padding-x: 1rem;
            --tblr-alert-padding-y: 0.3rem;
            --tblr-alert-margin-bottom: 0rem;
        }
    </style>
    <style id="Modalcss">
        .modal-footer {
            flex-wrap: unset;
            padding-left: 20px;
        }

        .form-label {
            font-weight: unset;
        }

        #mdThemMoi .modal-dialog {
            min-width: 55% !important;

        }
        .apexcharts-text {
            fill: #ffffff !important;
        }
    </style>

    <div id="DivMain">
         <div id="DivThongKe" class="row" style="margin-bottom:4px">
            <div class="col-md-4 d-flex">
                <div class="card w-100">
                    <div class="card-body d-flex align-items-center justify-content-center">
                        <div id="XepLoaiChart" style="height: 100px; width: 100%;"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-8" style="padding-left:unset; display: flex;">
                <div class="card w-100">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12">
                                <h3 style="color:darkgreen" class="card-title"><i class="fa fa-th-list"
                                        aria-hidden="true"></i> Tổng số: <b><u><b id="txtTongPhoi">0</b></u> <b> phôi bằng</b> trong
                                        đó:</b></h3>
                            </div>
                        </div>
                        <hr style="margin-top:4px">
                        <div class="row" id="LisdSoLuongPhoi">

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="DivTimKiem" class="mb-2">
            <div class="row flex-row-reverse">
                <div class="col-md-8">

                    <div class="text-end">
                          <div class="btn-group" style="height:28px">
                        <a id="btnAnHienTQ" style="color: #41d81e; margin-top: 0px; padding: 4px; cursor:pointer"><i id="iconAnHien" class="fa fa-eye-slash" aria-hidden="true"></i><span id="textAnHien"> Ẩn
                                trang tổng quan</span></a>
                    </div>
                        <button style="" class="btn position-relative" id="">
                            <select class="form-control" id="CbSapXep" tabindex="0"
                                style="padding: 0.05rem 0.55rem !important;">
                                <option value="SoQuyetDinh">Số</option>
                                <option value="NgayLap">Ngày lập</option>
                            </select>&ensp;&ensp;
                            <span id="BtnSapXepTangGiam">
                                <svg xmlns="http://www.w3.org/2000/svg"
                                    class="icon icon-tabler icon-tabler-sort-ascending-letters" width="24"
                                    height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                    <path d="M15 10v-5c0 -1.38 .62 -2 2 -2s2 .62 2 2v5m0 -3h-4" />
                                    <path d="M19 21h-4l4 -7h-4" />
                                    <path d="M4 15l3 3l3 -3" />
                                    <path d="M7 6v12" />
                                </svg>
                            </span>
                        </button>
                        <div class="btn-group" role="group">
                            <input type="radio" class="btn-check height-button-icon" name="btn-layout" id="btn-layout-1"
                                autocomplete="off" checked="">
                            <label style="margin-bottom: 0px" for="btn-layout-1" class="btn btn-icon">
                                <svg class="icon icon-tabler icon-tabler-list" width="20" height="20"
                                    viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M9 6l11 0"></path>
                                    <path d="M9 12l11 0"></path>
                                    <path d="M9 18l11 0"></path>
                                    <path d="M5 6l0 .01"></path>
                                    <path d="M5 12l0 .01"></path>
                                    <path d="M5 18l0 .01"></path>
                                </svg>
                            </label>
                            <input type="radio" class="btn-check height-button-icon" name="btn-layout" id="btn-layout-2"
                                autocomplete="off">
                            <label style="margin-bottom: 0px" for="btn-layout-2" class="btn btn-icon">
                                <svg class="icon icon-tabler icon-tabler-list-details" width="20" height="20"
                                    viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M13 5h8"></path>
                                    <path d="M13 9h5"></path>
                                    <path d="M13 15h8"></path>
                                    <path d="M13 19h5"></path>
                                    <path d="M3 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z">
                                    </path>
                                    <path d="M3 14m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z">
                                    </path>
                                </svg>
                            </label>
                        </div>
                        <button type="button" class="nts-color-them btn btn-primary" id="btnThemMoi"><i
                                class="fa fa-plus"></i>&ensp;Thêm mới
                            (F2)</button>
                        <div class="btn-group">
                            <div class="dropdown d-inline">
                                <button class="btn btn-primary dropdown-toggle-hide-arrow height-button-icon" type="button"
                                    id="growthReportId" data-bs-toggle="dropdown" aria-haspopup="true"
                                    aria-expanded="false">
                                    <i class="blue fa fa-ellipsis-h"></i>
                                </button>
                                <div class="dropdown-menu dropdown-menu-lg-end w-auto">
                                    <a class="nts-color-in dropdown-item textsize-item" href="javascript:void(0);"
                                        id="btnPrint" onclick="previewExportv1('pdf',GridMainLuoi);return false;"><i
                                            class="text-warning fa fa-print iconsize-item"></i>&ensp; In</a>
                                    <a class="nts-color-excel dropdown-item textsize-item" href="javascript:void(0);"
                                        id="btnExport" onclick="previewExportv1('excel',GridMainLuoi);return false;"><i
                                            class="text-success fa fa-file-excel-o iconsize-item"></i>&ensp; Xuất
                                        Excel</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div style="display: flex; align-items: center" class="DivTimKiem">
                        <div class="input-icon">
                            <input type="text" value="" class="form-control"
                                placeholder="Nội dung tìm kiếm ..." id="SearchKey" autocomplete="off"
                                style="border-top-right-radius: 0px; border-bottom-right-radius: 0px; ">
                            <span class="input-icon-addon">
                                <i class="fa fa-search"></i>
                            </span>
                        </div>
                        <button id="TimKiemNangCao" class="btn btn-primary height-button-icon" type="button"
                            style="border-top-left-radius: 0px;border-bottom-left-radius:0px;">
                            <span class="fa fa-sliders" aria-hidden="true"> </span>
                        </button>
                    </div>
                    <div id="KhungTimKiem" class="bg-white mt-1 px-4 py-2 border rounded shadow position-absolute"
                        style="z-index: 99; width:380px ; display:none">
                        <div class="row">
                            <div class="col text-primary fw-bold">
                                <i class="fa fa-search"></i> Tìm kiếm nâng cao
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label" for="TuNgay_Loc">Từ ngày</label>
                                <input type="text" class="form-control date-picker" id="TuNgay_Loc"
                                    autocomplete="off" data-date-format="dd/mm/yyyy">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="DenNgay_Loc">Đến ngày</label>
                                <input type="text" class="form-control date-picker" id="DenNgay_Loc"
                                    autocomplete="off" data-date-format="dd/mm/yyyy">
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label" for="CapHocID_Loc">Cấp học</label>
                                <select class="form-control input-sm" id="CapHocID_Loc" tabindex="0">
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="LoaiPhoiVanBangChungChiID_Loc">Loại phôi</label>
                                <select class="form-control input-sm" id="LoaiPhoiVanBangChungChiID_Loc" tabindex="0">
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label" for="LyDoID_Loc">Lý do</label>
                                              <select class="form-control" id="LyDoID_Loc" tabindex="0" required>
                                            </select>
                            </div>

                        </div>
                         <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label" for="DonViID_Loc">Đơn vị sử dụng</label>
                                              <select class="form-control" id="DonViID_Loc" tabindex="0" required>
                                            </select>
                            </div>

                        </div>
                        <hr />
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group" style="text-align:right">
                                    <button type="button" id="DongTimKiem" class="btn btn-outline-danger"><i
                                            class="fa fa-times"></i>&ensp;Đóng</button>&ensp;
                                    <button type="button" id="TimKiem" class="btn btn-success ms-auto"><i
                                            class="fa fa-search"></i>&ensp;Tìm kiếm</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="DivDanhSach">
            <div class="row">
                <div class="col-md-12">
                    <div id="GridMainDS">
                    </div>
                </div>
            </div>
        </div>
        <div id="DivLuoi" style="display:none">
            <div class="row">
                <div class="col-md-12">
                    <div id="GridMainLuoi" class="GridData">

                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- Create Modal --}}
    <div class="modal modal-blur fade" id="mdThemMoi" tabindex="-1" aria-modal="true" role="dialog"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tieuDeModal"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <fieldset class="KhungVien">
                        <legend>Thông tin thu hồi/hủy bỏ phôi văn bằng, chứng chỉ</legend>
                        <div class="row" style="margin: 5px;">
                            <div class="col-md-12" style="padding-left: 10px">
                                <div class="row">
                                    <div class="col-lg-4">
                                        <div class="mb-1">
                                            <label class="form-label" for="SoQuyetDinh">Số</label>
                                            <input type="text" class="form-control" id="SoQuyetDinh" required>
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="mb-1">
                                            <label class="form-label" for="NgayLap">Ngày lập</label>
                                            <input type="text" class="form-control date-picker" id="NgayLap"
                                                autocomplete="off" data-date-format="dd/mm/yyyy" >
                                        </div>
                                    </div>
                                      <div class="col-lg-4">
                                        <div class="mb-1">
                                            <label class="form-label" for="CapHocID">Cấp học</label>
                                            <select class="form-control" id="CapHocID" tabindex="0" required>
                                            </select>
                                        </div>
                                    </div>

                                </div>
                                <div class="row">
                                   <div class="col-lg-4">
                                        <div class="mb-1">
                                            <label class="form-label" for="NhanVienID_NguoiLap">Người lập</label>
                                            <select class="form-control" id="NhanVienID_NguoiLap" tabindex="0" required>
                                            </select>
                                        </div>
                                    </div>


                                    <div class="col-lg-4">
                                        <div class="mb-1">
                                            <label class="form-label" for="ChucVuID_NguoiLap">Chức vụ</label>
                                            <select class="form-control" id="ChucVuID_NguoiLap" tabindex="0" required>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-lg-4">
                                        <div class="mb-1">
                                            <label class="form-label" for="LoaiPhoiVanBangChungChiID">Loại phôi</label>
                                            <select class="form-control" id="LoaiPhoiVanBangChungChiID" tabindex="0"
                                                required>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">

                                    <div class="col-lg-8">
                                        <div class="mb-1">
                                            <label class="form-label" for="DonViID_SuDung">Đơn vị sử dụng</label>
                                            <select class="form-control" id="DonViID_SuDung" tabindex="0" required>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="mb-1">
                                            <label class="form-label" for="LyDoID">Lý do</label>
                                              <select class="form-control" id="LyDoID" tabindex="0" required>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-4">
                                        <div class="mb-1">
                                            <label class="form-label" for="SoLuongHuy">Số lượng</label>
                                            <input type="number" class="form-control" id="SoLuongHuy"
                                                required>
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="mb-1">
                                            <label class="form-label" for="SoHieuTu">Số hiệu từ</label>
                                            <input type="text" class="form-control" id="SoHieuTu" required>
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="mb-1">
                                            <label class="form-label" for="SoHieuDen">Số hiệu đến</label>
                                            <input type="text" class="form-control" id="SoHieuDen" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="mb-1">
                                            <label class="form-label" for="GhiChu">Ghi chú</label>
                                            <textarea class="form-control" id="GhiChu" rows="4"></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        @include('partials.dinh-kem-v2', [
                                            'id' => 'HuyPhoiVBCC',
                                        ])
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="form-group">
                                        <label class="col-md-2 control-label no-padding-right"></label>
                                        <div class="col-md-10" id="list-file">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div class="modal-footer">
                    <div style="display: contents; align-items: center; justify-content: space-between">
                        <div class="col-md-6" style="display: flex; align-items: center">
                        </div>
                        <div class="col-md-6">
                            <div style="float:right;text-align: right">
                                <a href="#" class="nts-color-dong btn btn-outline-danger" data-bs-dismiss="modal">
                                    <i class="fa fa-close"></i>&nbsp;Đóng (F4)
                                </a>
                                <a href="#" id="btnLuuVaDong" class="nts-color-luu btn btn-success ms-auto">
                                    <i class="fa fa-save"></i>&ensp;Lưu và đóng (F9)
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- Create Modal --}}
    <div class="modal modal-blur fade" id="mdXemThongTin" tabindex="-1" aria-modal="true" role="dialog"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tieuDeModalXem"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <fieldset class="KhungVien">
                        <legend>Thông tin</legend>
                        <div class="row" style="margin: 5px;">
                            <div class="col-md-12" style="padding-left: 10px">
                                <div class="row">
                                    <div class="col-lg-4">
                                        <div class="mb-1">
                                            <label class="form-label" id="lblSoPhieu">Số phiếu:</label>

                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="mb-1">
                                            <label class="form-label" id="lblNgayHuy">Ngày hủy:</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="mb-1">
                                            <label class="form-label" id="lblNhanVienID_HuyPhoi">Người chiệu trách
                                                nhiệm:</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-4">
                                        <div class="mb-1">
                                            <label class="form-label" id="lblCapHocID">Cấp học:</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-8">
                                        <div class="mb-1">
                                            <label class="form-label" id="lblLoaiPhoiVanBangChungChiID">Loại phôi:</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-4">
                                        <div class="mb-1">
                                            <label class="form-label" id="lblNamTotNghiep">Năm tốt nghiệp:</label>
                                        </div>
                                    </div>

                                </div>
                                <div class="row">
                                    <div class="col-lg-4">
                                        <div class="mb-1">
                                            <label class="form-label" id="lblSoLuongHuy">Số lượng hủy:</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="mb-1">
                                            <label class="form-label" id="lblSoSeriBatDau">Số seri bắt đầu:</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="mb-1">
                                            <label class="form-label" id="lblSoSeriKetThuc">Số seri kết thúc:</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="mb-1">
                                            <label class="form-label" id="lblLyDoHuy">Lý do hủy:</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="mb-1">
                                            <label class="form-label" id="lblGhiChu">Ghi chú:</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-12">

                                    </div>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div class="modal-footer">
                    <div style="display: contents; align-items: center; justify-content: space-between">
                        <div class="col-md-6" style="display: flex; align-items: center">
                        </div>
                        <div class="col-md-6">
                            <div style="float:right;text-align: right">
                                <a href="#" class="nts-color-dong btn btn-outline-danger" data-bs-dismiss="modal">
                                    <i class="fa fa-close"></i>&nbsp;Đóng
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="mdQPheDuyet" tabindex="-1" aria-modal="true" role="dialog"
    data-bs-backdrop="static">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document" style=" width: 52%; ">
        <div class="modal-content p-0 rounded-3 overflow-hidden">
            <!-- Modal Header -->
            <div class="modal-header py-2 px-3" style="background:#fd7e14;color:#fff;">
                <h5 class="modal-title fw-bold text-uppercase" id="tieuDe_mdQPheDuyet">Phê duyệt thu hồi/hủy bỏ phôi bằng
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <!-- Modal Body -->
            <div class="modal-body px-4 py-3">
                <div class="row">
                    <!-- Illustration -->
                    <div class="col-md-5 d-flex align-items-center justify-content-center mb-2 mb-md-0">
                        <img src="/img/Logo-note2.jpeg" alt="Quyết định" class="img-fluid" style="max-width:350px;">
                    </div>
                    <!-- Right Side: Content -->
                    <div class="col-md-7">
                        <!-- Alert/Instruction -->
                        <div class="border border-warning rounded mb-3 px-3 py-2"
                            style="background:#fff7e6;font-size:15px;">

                            <span id="alertMessage">
                                Bạn đang thực hiện phê duyệt thu hồi/hủy bỏ phôi văn bằng, chứng chỉ số: <b id="SoDeNghiPD"></b> ngày lập <b id="NgayDeNghiPD"></b> của <b id="DonViDeNghiPD"></b>.
                                Vui lòng điền đầy đủ thông tin bên dưới và nhấn vào nút <b>"Phê duyệt"</b> để thực hiện thao tác phê duyệt phiếu thu hồi/hủy bỏ phôi văn bằng, chứng chỉ.
                            </span>
                        </div>
                        <!-- Fieldset -->
                        <fieldset class="KhungVien border rounded-3 p-3 mb-1">
                            <legend class="float-none w" id="fieldsetLegend">
                                Thông tin phê duyệt
                            </legend>
                            <form>
                                <div class="row g-2">
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label mb-1" for="NgayPheDuyet" style="">Ngày phê duyệt
                                            <span class="text-danger">(*)</span></label>
                                        <div class="input-group input-group-sm">
                                            <input type="text" class="form-control date-picker" id="NgayPheDuyet"
                                                name="NgayKy" autocomplete="off" data-date-format="dd/mm/yyyy"
                                                placeholder="dd/MM/yyyy" required>

                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6"></div>
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label  mb-1" for="NhanVienID_PheDuyet" style="">Người phê duyệt</label>
                                        <select class="form-select" id="NhanVienID_PheDuyet">
                                            <option>-Chọn-</option>
                                        </select>
                                    </div>
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label  mb-1" for="ChucVuID_PheDuyet" style="">Chức
                                            vụ</label>
                                        <select class="form-select" id="ChucVuID_PheDuyet">
                                            <option>-Chọn-</option>
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label mb-1" for="NoiDung_PheDuyet" style="">Nội dung phê duyệt</label>
                                        <div id="NoiDung_PheDuyet" class="form-control" contenteditable="true"
                                            style=" min-height: 120px;"></div>
                                    </div>
                                </div>
                            </form>
                        </fieldset>
                    </div>
                </div>
            </div>
            <!-- Modal Footer -->
            <div class="modal-footer justify-content-end py-2 px-3" style="background:#f4f4f4;">
                <button type="button" class="btn btn-outline-danger fw-bold px-3 me-2 nts-color-dong" data-bs-dismiss="modal">
                    <i class="fa fa-times"></i>&ensp;Đóng (F4)
                </button>
                <button type="button" class="btn btn-warning text-white fw-bold px-3" id="btnBanHanh">
                    <i class="fa fa-check-circle nts-iconThaoTacs" aria-hidden="true"></i>&ensp;Phê duyệt (F9)
                </button>
            </div>
        </div>
    </div>
</div>
    <input type="hidden" id="HuyPhoiVBCCID" />
@endsection
@include('layouts.XemDSDinhKem')
@push('scripts')
    <script>
        window.Laravel = window.Laravel || {}; // 👈 Bảo vệ trước khi gán
        window.Laravel.local = {
            getTiepNhanPhoiVBCC: "{{ route('tiepnhanphoivbcc.getTiepNhanPhoiVBCC') }}",
            uploadFile: "{{ route('api.files.upload') }}",
            imgLuoi: `{{ asset('img/imgBienBanKho.png') }}`,
            maTuTangUrl: `{{ route('dungchung.lay-ma-tutang', ['kyhieuLoaiPhieu' => 'TN' . now()->year, 'bangDuLieu' => 'huy_phoi_v_b_c_c_s', 'cotDuLieu' => 'SoPhieu']) }}`,
            comboNhanVien: "{{ route('dungchung.danhmuc.comboNhanVien') }}",
            getcaphoc: "{{ route('caphoc.ldb.getcaphoc') }}",
            getloaiphoivanbangchungchi: "{{ route('loaiphoivanbangchungchi.getloaiphoivanbangchungchi') }}",
            getDanhSachTiepNhanCoCapPhat: "{{ route('capphatphoivbcc.getDanhSachTiepNhanCoCapPhat') }}",
            listChucVuUrl: "{{ route('dungchung.danhmuc.comboChucVu') }}",
            getListDonVi: `{{{ route('dungchung.danhmuc.comboDonVi') }}}`,
            getListLyDo: `{{{ route('huyphoivbcc.getListLyDo') }}}`,
            soPhieuTuTangUrl: `{{ route('dungchung.lay-ma-tutang', ['kyhieuLoaiPhieu' => 'MaPhieuHuyPhoiBang', 'bangDuLieu' => 'huy_phoi_v_b_c_c_s', 'cotDuLieu' => 'SoPhieu']) }}`,
             getThongKeHuyPhoiTheoTenLyDo: "{{ route('huyphoivbcc.getThongKeHuyPhoiTheoTenLyDo') }}",


        };
    </script>
    <script src="{{ asset('js/quanly/HuyPhoiVBCC.js') }}?v={{ time() }}"></script>
@endpush
