@extends('layouts.layouts')

{{-- shorthand section, no @endsection needed --}}
@section('title', '<PERSON>h sách học sinh')

@section('content')
    <style type="text/css">
        #KhungTimKiem .fw-bold {
            color: #f76707 !important;
        }

        .fs-big {
            white-space: break-spaces;
        }

        .list-item {
            border-radius: 6px;
            box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.1);
        }

        #Grid1.tabulator {
            border: 1px solid #ffffff00 !important;
            background-color: #ffffff00 !important;
        }

        #Grid1.tabulator .tabulator-tableholder {
            background-color: #ffffff00 !important;
        }

        #Grid1.tabulator .tabulator-tableholder .tabulator-table {
            background-color: #ffffff00 !important;
        }

        #Grid1 .tabulator-row.tabulator-selected {
            background-color: #ffffff00 !important;
        }

        .TieuDeLabel span {
            font-weight: bold;
        }

        #Grid1 .tabulator-row {
            background-color: #ffffff00 !important;
            border: unset !important;
        }

        #Grid1 .tabulator-row>.tabulator-cell {
            border-right: 0px solid #ffffff00 !important;
            padding: 2px 4px 2px 2px !important;
        }

        #Grid1.tabulator .tabulator-footer {
            border: 1px solid #dedede !important;
            padding: 0px !important;
        }

        .tabulator-row.tabulator-selectable:hover>.tabulator-cell .show-or-hide {
            display: block !important;
            background: white;
            padding: 5px 13px;
        }
    </style>
    <style type="text/css">
        .span-trangthai {
            text-align: center;
            padding: 2px 10px;
            --tblr-alert-color: #ffffff00;
            --tblr-alert-bg: var(--tblr-surface);
            border: var(--tblr-border-width) var(--tblr-border-style) rgb(255 255 255 / 0%);
            border-left: .25rem var(--tblr-border-style) var(--tblr-alert-color);
            box-shadow: rgba(24, 36, 51, .04) 0 2px 4px 0;
            color: white;
        }

        /* .list-item hr {
            color: inherit;
            background-color: #f76707;
            border-top: 2px solid #f76707 !important;
            margin: 0 !important;
        } */

        .profile-picture {
            width: 100%;
            height: 130px;
            display: flex;
        }

        #mdThemMoi {
            width: 100% !important;
            max-width: 100% !important;
            margin: 0 !important;
            padding-right: 0px !important;
            padding: 0 !important;
            scroll-behavior: smooth;
        }

        #mdThemMoi .modal-content {
            min-height: 100vh !important;
        }

        #mdThemMoi .modal-dialog {
            width: 100% !important;
            max-width: 100% !important;
            padding: 0 !important;
            margin: unset !important;
        }

        #mdNhanExcel .modal-dialog {
            width: 100% !important;
            max-width: 100% !important;
        }



        @media (max-width: 468px) {
            #mdThemMoi .modal-dialog .modal-body {
                margin-bottom: 40px !important;
            }

        }

        #chonAvatar_DoiTuong {
            cursor: pointer;
        }

        html {
            scroll-behavior: smooth;
        }

        .sticky {
            position: -webkit-sticky;
            /* Safari */
            position: sticky;
            top: 0px;
        }

        .sectionThongTin {
            position: relative;
        }

        #Grid2 .tabulator-row table {
            vertical-align: middle;
            border-collapse: collapse;
        }

        #Grid2 .tabulator-row table img {
            border: 2px solid #ccc;
        }

        #Grid2 .tabulator-row table tr td {
            border: none;
        }

        #Grid2 .tabulator-row table tr td:first-of-type {
            width: 60px;
        }

        #Grid2 .tabulator-row table tr td span {
            text-align: left;
            font-size: 12px;
        }

        #Grid2 .tabulator-table {
            width: 100%;
        }

        #Grid2 strong {
            /* color: var(--tblr-primary); */
            font-size: 14px;
            text-align: left;
        }

        #Grid2 .tabulator-row {
            padding-left: 10px
        }

        #Grid2 img {
            width: 50px;
            height: 50px;
            border-radius: 100%;
            object-fit: cover;
        }

        .info-container {
            background-image: url('{{ asset('img/bg1.jpg') }}');
            color: #fff;
        }

        .backdrop {
            width: 100%;
            height: 200px;
            background-size: cover;
            background-position: 50% 0;
            background-repeat: no-repeat;
            position: relative;
        }

        .backdrop:before {
            position: absolute;
            content: "";
            width: 100%;
            top: 0;
            bottom: 0;
            /* background-color: rgba(2, 13, 24, .75); */
        }

        .info-container-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            padding-top: 15px;
            position: relative;
        }

        .info-container-header-left {
            display: flex;
            align-items: center;
            gap: 0 10px;
        }

        .info-img-container {
            width: 64px;
            height: 64px;
            object-fit: cover;
            border-radius: 100%;
            border: 2px solid #ccc;
        }

        .info-img {
            width: 100%;
            height: 100%;
            border-radius: inherit;
        }

        .info-title {
            font-size: 15px;
        }

        .info-subtitle {
            font-size: 13px;
        }



        .info-body {
            margin: 0 10px !important;
            margin-top: -100px !important;
        }

        #Grid2 .tabulator-footer-contents {
            flex-direction: column !important;
            align-items: unset !important;
            text-align: left;
        }

        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: fixed;
            background-color: white;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 100;
            border-radius: 8px;
            left: 30px;
        }

        .menu-tabu {
            float: left;
            margin-left: 8px;
            padding: 5px 10px 5px 10px;
            text-decoration: none !important;
            width: 85%;
            display: block !important;
            text-align: left;
        }

        .dropdown:hover .dropdown-content {
            display: block;
        }


        .dropdown-menu-arrow:before {
            content: "";
            position: absolute;
            top: -.25rem;
            left: .75rem;
            display: block;
            background: inherit;
            width: 14px;
            height: 14px;
            transform: rotate(45deg);
            transform-origin: center;
            border: 1px solid;
            border-color: inherit;
            z-index: -1;
            clip: rect(0, 9px, 9px, 0);
            color: white;
        }

        .title-section {
            background: blue;
            width: fit-content;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            position: absolute;
            top: -15px;
        }

        .width-edit-hogiadinh {
            width: 240px;
        }

        .nav-tabs .nav-item.show .nav-link,
        .nav-tabs .nav-link.active {
            border-color: var(--tblr-card-bg);
        }

        .nav-link {
            position: relative;
        }

        .nav-tabs .nav-link:focus,
        .nav-tabs .nav-link:hover {
            border-color: white
        }

        #avatar_DoiTuong,
        #avatar {
            width: 170px;
            height: 130px;
            object-fit: cover;
        }


        @media (min-width: 1068px) {
            #mdCauhinhluoi .modal-dialog {
                width: 95% !important;
                max-width: 95% !important;
            }
        }

        @media (max-width: 992px) {
            .page-wrapper {
                margin-left: unset !important;
            }

            .tabulator-cell[tabulator-field="ThongTinHoGiaDinh"] {
                height: auto !important;
            }
        }

        /*độ rộng hình ảnh*/
        .profile-picture {
            width: 100%;
            height: 130px;
        }

        .profile-picture .img-thumbnail {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        #Grid2 .tabulator-footer-contents .tabulator-page-counter {
            text-align: right !important;
            padding-bottom: 3px;
        }

        .KhungVien {
            padding: 5px 10px 0px 10px !important;
        }

        .GridData .dropdown-item {
            padding: 5px 12px !important;
        }

        .line {
            flex-grow: 2;
            border-bottom: 1px solid #dadcde;
            margin: 0 10px;
        }

        .tabulator-cell .dropdown-menu-end {
            position: fixed !important;
        }

        .node:hover>image {
            width: 55px;
            height: 55px;
            transition: 0.3s;
            margin-left: 5px !important;
            margin-top: 5px !important;
        }

        svg.simple-diagram .node:hover {
            stroke: unset !important;
        }

        #diagram div svg {
            height: 520px !important
        }

        .btnThemNhanh:hover {
            border-color: #dadfe5 !important;
            text-decoration: none;
        }

        /*nhật ký*/
        .vertical-timeline {
            width: 100%;
            position: relative;
            padding: 1.5rem 0 1rem;
            margin-top: 1rem;
        }

        .vertical-timeline-element {
            position: relative;
            margin: 0 0 1rem;
        }

        .vertical-timeline--animate .vertical-timeline-element-icon.bounce-in {
            visibility: visible;
            animation: cd-bounce-1 .8s;
        }

        .vertical-timeline-element-icon {
            left: 10px !important;
        }

        .vertical-timeline-element-icon {
            position: absolute;
            top: 0;
            left: 60px;
        }

        .vertical-timeline-element-content {
            margin-left: 50px !important;
            padding-right: 10px !important;
        }

        .vertical-timeline-element-content {
            position: relative;
            margin-left: 90px;
            font-size: .8rem;
        }

        .vertical-timeline-element-content:after {
            content: "";
            display: table;
            clear: both;
        }

        .vertical-timeline::before {
            left: 24px !important;
        }

        .vertical-timeline-success::before {
            opacity: 1;
            background-color: rgba(47, 179, 68, 1) !important;
        }

        .vertical-timeline::before {
            content: '';
            position: absolute;
            top: 0;
            left: 67px;
            height: 100%;
            width: 4px;
            background: #e9ecef;
            border-radius: 0.25rem;
        }

        .vertical-timeline-element-icon .badge-dot-xl {
            box-shadow: 0 0 0 5px #fff;
        }

        .badge-success {
            color: #fff;
            background-color: #28a745;
        }

        .badge-dot-xl {
            width: 18px;
            height: 18px;
            position: relative;
        }

        .offcanvas {
            width: 500px !important;
        }

        .bg-transparent {
            --tblr-box-shadow-border: inset 0 0 0 1px var(--tblr-border-color-translucent);
            --tblr-bg-surface-secondary: var(--tblr-gray-100);
            --tblr-avatar-bg: var(--tblr-bg-surface-secondary);
            justify-content: center;
            color: var(--tblr-secondary);
            vertical-align: bottom;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            background: var(--tblr-avatar-bg) no-repeat center;
            border-radius: var(--tblr-border-radius);
            box-shadow: var(--tblr-avatar-box-shadow);
            padding-left: 12px;
            padding-top: 8px;
        }

        .timeline-yellow {
            border: 1px solid #f59f004b;
        }

        .timeline-green {
            border: 1px solid #2fb3444b;
        }

        .timeline-blue {
            border: 1px solid #0054a63e;
        }

        .timeline-orange {
            border: 1px solid #f7670746;
        }

        .timeline-red {
            border: 1px solid #d639394b;
        }

        .section-diachi .label-text {
            color: #018ec9;
            font-weight: bold;
        }

        #box-double-input span.select2-selection.select2-selection--single {
            border-top-right-radius: unset;
            border-bottom-right-radius: unset;
        }

        #box-double-input input#SoHoKhau {
            border-top-left-radius: unset;
            border-bottom-left-radius: unset;
        }
    </style>
    <div class="main-content divmain">
        <div id="DivThongKe" class="row" style="margin-bottom:4px">
            <div class="col-md-4 d-flex">
                <div class="card w-100">
                    <div class="card-body d-flex align-items-center justify-content-center">
                        <div id="XepLoaiChart" style="height: 100px; width: 100%;"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-8" style="padding-left:unset; display: flex;">
                <div class="card w-100">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12">
                                <h3 style="color:darkgreen" class="card-title"><i class="fa fa-th-list"
                                        aria-hidden="true"></i> Tổng số: <b><u><b id="txtTongHS">0</b> học sinh</u> trong
                                        đó:</b></h3>
                            </div>
                        </div>
                        <hr style="margin-top:4px">
                        <div class="row">
                            <div class="col-md-6" style="border-right: 1px solid #07a607">
                                <h3 style="color: #d81e1e" class="card-title">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17"
                                        viewBox="0 0 17 17" fill="none">
                                        <path
                                            d="M14.5303 3.6184L8.3724 1.56577C8.26706 1.53065 8.15317 1.53065 8.04783 1.56577L1.88993 3.6184C1.78775 3.65246 1.69888 3.71781 1.63591 3.80519C1.57294 3.89257 1.53905 3.99755 1.53906 4.10526V9.23684C1.53906 9.37293 1.59313 9.50346 1.68936 9.59969C1.7856 9.69593 1.91612 9.74999 2.05222 9.74999C2.18832 9.74999 2.31884 9.69593 2.41508 9.59969C2.51131 9.50346 2.56538 9.37293 2.56538 9.23684V4.81726L4.72 5.53504C4.14755 6.45988 3.96551 7.5741 4.21386 8.63304C4.46221 9.69199 5.12064 10.6091 6.04459 11.183C4.88998 11.6358 3.89189 12.455 3.16192 13.5749C3.12396 13.6314 3.09759 13.6948 3.08435 13.7615C3.07111 13.8282 3.07125 13.8969 3.08478 13.9635C3.09832 14.0302 3.12496 14.0934 3.16317 14.1497C3.20137 14.206 3.25038 14.2541 3.30734 14.2912C3.3643 14.3284 3.42808 14.3538 3.49497 14.3661C3.56186 14.3784 3.63052 14.3773 3.69697 14.3628C3.76341 14.3483 3.82632 14.3208 3.88203 14.2818C3.93773 14.2427 3.98513 14.1931 4.02146 14.1356C4.98813 12.6525 6.51477 11.8026 8.21012 11.8026C9.90546 11.8026 11.4321 12.6525 12.3988 14.1356C12.474 14.2474 12.5903 14.3252 12.7224 14.352C12.8546 14.3788 12.9919 14.3526 13.1049 14.2789C13.2178 14.2053 13.2972 14.0901 13.3259 13.9584C13.3546 13.8267 13.3303 13.6889 13.2583 13.5749C12.5283 12.455 11.5264 11.6358 10.3756 11.183C11.2987 10.6091 11.9565 9.69261 12.2048 8.63444C12.4531 7.57627 12.2717 6.46282 11.7002 5.53825L14.5303 4.59532C14.6325 4.56128 14.7214 4.49594 14.7844 4.40856C14.8474 4.32117 14.8813 4.21618 14.8813 4.10846C14.8813 4.00074 14.8474 3.89575 14.7844 3.80837C14.7214 3.72099 14.6325 3.65565 14.5303 3.6216V3.6184ZM11.2891 7.69736C11.2892 8.18413 11.1739 8.66399 10.9527 9.09758C10.7315 9.53117 10.4106 9.90613 10.0164 10.1917C9.62218 10.4772 9.16589 10.6653 8.68495 10.7403C8.20401 10.8154 7.71212 10.7754 7.24964 10.6235C6.78717 10.4717 6.36728 10.2124 6.02444 9.86682C5.6816 9.52127 5.42557 9.09937 5.27736 8.63571C5.12915 8.17206 5.09298 7.67987 5.17182 7.19953C5.25067 6.71919 5.44227 6.26439 5.73092 5.87244L8.04783 6.64218C8.15317 6.6773 8.26706 6.6773 8.3724 6.64218L10.6893 5.87244C11.0791 6.40102 11.2893 7.04059 11.2891 7.69736ZM8.21012 5.61715L3.67508 4.10526L8.21012 2.59336L12.7451 4.10526L8.21012 5.61715Z"
                                            fill="#D63939" />
                                    </svg>
                                    Chưa xét tốt nghiệp: <b><u><b id="txtChuaXetTN">0</b> học
                                            sinh</u></b>
                                </h3>
                                <h3 style="color: #9E24E9" class="card-title">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="17"
                                        viewBox="0 0 20 17" fill="none">
                                        <g clip-path="url(#clip0_17947_41925)">
                                            <path
                                                d="M11.8433 0.886737L9.79911 0L7.75496 0.886737L5.47933 1.17616L4.44807 2.90037L2.81055 4.25511L3.18533 6.1579L2.81055 8.06068L4.44807 9.41542L5.47933 11.1396L7.75496 11.4291L9.79911 12.3158L11.8433 11.4291L14.1189 11.1396L15.1501 9.41542L16.7877 8.06068L16.4129 6.1579L16.7877 4.25511L15.1501 2.90037L14.1189 1.17616L11.8433 0.886737ZM13.3081 2.11113L14.1458 3.51205L15.4759 4.61226L15.1722 6.1579L15.4759 7.70353L14.1458 8.80374L13.3081 10.2047L11.4587 10.4397L9.79911 11.1602L8.13954 10.4397L6.29013 10.2047L5.45239 8.80374L4.12228 7.70353L4.42725 6.1579L4.12106 4.61226L5.45239 3.51205L6.29013 2.11113L8.13954 1.87611L9.79911 1.15563L11.4599 1.87611L13.3081 2.11113Z"
                                                fill="#9E24E9" />
                                            <path
                                                d="M4.90039 12.1044V16.4211L9.79949 15.3947L14.6986 16.4211V12.1044L12.227 12.4184L9.79949 13.4714L7.37199 12.4184L4.90039 12.1044Z"
                                                fill="#9E24E9" />
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_17947_41925">
                                                <rect width="19.5964" height="16.4211" fill="white" />
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    Đã xét tốt nghiệp: <b><u><b id="txtDaXetTN">0</b>
                                            học sinh</u></b>
                                </h3>
                            </div>
                            <div class="col-md-6">
                                <h3 style="color: #7AA802" class="card-title">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17"
                                        viewBox="0 0 17 17" fill="none">
                                        <path
                                            d="M2.73756 2.05261C1.97809 2.05261 1.36914 2.66156 1.36914 3.42103V10.2631C1.36914 10.6261 1.51331 10.9741 1.76994 11.2308C2.02657 11.4874 2.37463 11.6316 2.73756 11.6316H8.21125V15.0526L10.2639 13L12.3165 15.0526V11.6316H13.6849C14.0479 11.6316 14.3959 11.4874 14.6525 11.2308C14.9092 10.9741 15.0534 10.6261 15.0534 10.2631V3.42103C15.0534 3.05811 14.9092 2.71004 14.6525 2.45341C14.3959 2.19678 14.0479 2.05261 13.6849 2.05261H2.73756ZM8.21125 3.42103L10.2639 4.78945L12.3165 3.42103V5.81577L14.3691 6.84209L12.3165 7.8684V10.2631L10.2639 8.89472L8.21125 10.2631V7.8684L6.15861 6.84209L8.21125 5.81577V3.42103ZM2.73756 3.42103H6.15861V4.78945H2.73756V3.42103ZM2.73756 6.15788H4.79019V7.5263H2.73756V6.15788ZM2.73756 8.89472H6.15861V10.2631H2.73756V8.89472Z"
                                            fill="#7AA802" />
                                    </svg> Đã cấp cấp bằng: <b><u><b id="txtDaCapBang">0</b> học
                                            sinh</u></b>
                                </h3>
                                <h3 style="color: #F78B2D" class="card-title">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17"
                                        viewBox="0 0 17 17" fill="none">
                                        <path
                                            d="M8.21083 13C9.34447 13 10.2635 12.081 10.2635 10.9473C10.2635 9.81371 9.34447 8.89471 8.21083 8.89471C7.0772 8.89471 6.1582 9.81371 6.1582 10.9473C6.1582 12.081 7.0772 13 8.21083 13Z"
                                            stroke="#F78B2D" stroke-width="1.02632" />
                                        <path
                                            d="M6.23612 10.92L5.52454 11.6316L4.51328 12.6011C4.2916 12.8139 4.18076 12.9199 4.14244 13.0102C4.09936 13.1063 4.09372 13.215 4.12663 13.315C4.15954 13.415 4.22863 13.4991 4.32033 13.5508C4.40381 13.598 4.55502 13.6124 4.85607 13.6425C5.02576 13.6589 5.11128 13.6671 5.18244 13.6924C5.25976 13.7193 5.33037 13.7625 5.38941 13.8192C5.44846 13.8759 5.49456 13.9447 5.52454 14.0208C5.55123 14.0892 5.56012 14.1707 5.57723 14.3342C5.60802 14.6229 5.62376 14.7673 5.67302 14.8474C5.78523 15.03 6.02265 15.1019 6.23681 15.0184C6.33055 14.9808 6.44139 14.8747 6.66307 14.6626L8.21144 13.1779L9.75981 14.6626C9.98149 14.8747 10.0923 14.9808 10.1861 15.0184C10.4002 15.1019 10.6377 15.03 10.7499 14.8474C10.7991 14.7673 10.8149 14.6229 10.8457 14.3342C10.8628 14.1707 10.8717 14.0892 10.8983 14.0208C10.9283 13.9447 10.9744 13.8759 11.0335 13.8192C11.0925 13.7625 11.1631 13.7193 11.2404 13.6924C11.3123 13.6671 11.3971 13.6589 11.5668 13.6425C11.8679 13.613 12.0191 13.598 12.1025 13.5508C12.1943 13.4991 12.2633 13.415 12.2963 13.315C12.3292 13.215 12.3235 13.1063 12.2804 13.0102C12.2421 12.9199 12.1313 12.8139 11.9096 12.6011L10.8977 11.6316L10.2641 10.9973"
                                            stroke="#F78B2D" stroke-width="1.02632" />
                                        <path
                                            d="M11.8512 12.313C13.2005 12.2987 13.9531 12.2138 14.4519 11.7144C15.0534 11.1136 15.0534 10.1455 15.0534 8.21051V5.47367C15.0534 3.53872 15.0534 2.57057 14.4519 1.96983C13.8512 1.36841 12.883 1.36841 10.9481 1.36841H5.4744C3.53946 1.36841 2.5713 1.36841 1.97056 1.96983C1.36914 2.57057 1.36914 3.53872 1.36914 5.47367V8.21051C1.36914 10.1455 1.36914 11.1136 1.97056 11.7144C2.49604 12.2405 3.30204 12.3062 4.79019 12.3144"
                                            stroke="#F78B2D" stroke-width="1.02632" />
                                        <path d="M6.15944 4.10522H10.2647M4.79102 6.49996H11.6331" stroke="#F78B2D"
                                            stroke-width="1.02632" stroke-linecap="round" />
                                    </svg> Chưa cấp bằng: <b><u><b id="txtChuaCapBang">0</b> học
                                            sinh</u></b>
                                </h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="DivTimKiem" class="row flex-row-reverse">
            <div class="col-md-8">
                <div class="text-end">
                    <div class="btn-group" style="height:28px">
                        <a id="btnAnHienTQ" style="color: #41d81e; margin-top: 0px; padding: 4px; cursor:pointer"><i
                                id="iconAnHien" class="fa fa-eye-slash" aria-hidden="true"></i><span id="textAnHien"> Ẩn
                                trang tổng quan</span></a>
                    </div>
                    <button style="" class="btn position-relative" id="">
                        <select class="form-control" id="CbSapXep" tabindex="0"
                            style="padding: 0.05rem 0.55rem !important;">
                            <option value="Hovaten">Họ và tên</option>
                            <option value="MaDoiTuong">Mã học sinh</option>
                        </select>&ensp;&ensp;
                        <span id="BtnSapXepTangGiam">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="icon icon-tabler icon-tabler-sort-ascending-letters" width="24" height="24"
                                viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                <path d="M15 10v-5c0 -1.38 .62 -2 2 -2s2 .62 2 2v5m0 -3h-4" />
                                <path d="M19 21h-4l4 -7h-4" />
                                <path d="M4 15l3 3l3 -3" />
                                <path d="M7 6v12" />
                            </svg>
                        </span>
                    </button>
                    <div class="btn-group" role="group">
                        <input type="radio" class="btn-check height-button-icon" name="btn-layout" id="btn-layout-1"
                            autocomplete="off" checked="">
                        <label style="margin-bottom: 0px" for="btn-layout-1" class="btn btn-icon">
                            <svg class="icon icon-tabler icon-tabler-list" width="20" height="20"
                                viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M9 6l11 0"></path>
                                <path d="M9 12l11 0"></path>
                                <path d="M9 18l11 0"></path>
                                <path d="M5 6l0 .01"></path>
                                <path d="M5 12l0 .01"></path>
                                <path d="M5 18l0 .01"></path>
                            </svg>
                        </label>
                        <input type="radio" class="btn-check height-button-icon" name="btn-layout" id="btn-layout-2"
                            autocomplete="off">
                        <label style="margin-bottom: 0px" for="btn-layout-2" class="btn btn-icon">
                            <svg class="icon icon-tabler icon-tabler-list-details" width="20" height="20"
                                viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M13 5h8"></path>
                                <path d="M13 9h5"></path>
                                <path d="M13 15h8"></path>
                                <path d="M13 19h5"></path>
                                <path d="M3 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z">
                                </path>
                                <path d="M3 14m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z">
                                </path>
                            </svg>
                        </label>
                    </div>
                    <button type="button" class="nts-color-them btn btn-primary" id="btnThemMoi"><i
                            class="fa fa-plus"></i>&ensp;Thêm mới
                        (F2)</button>
                    <div class="btn-group">
                        <div class="dropdown d-inline">
                            <button class="btn btn-primary dropdown-toggle-hide-arrow height-button-icon" type="button"
                                id="growthReportId" data-bs-toggle="dropdown" aria-haspopup="true"
                                aria-expanded="false">
                                <i class="blue fa fa-ellipsis-h"></i>
                            </button>
                            <div class="dropdown-menu dropdown-menu-lg-end w-auto">
                                <a class="nts-color-in dropdown-item textsize-item" href="javascript:void(0);"
                                    id="btnPrint" onclick="previewExportv1('pdf',tableHS);return false;"><i
                                        class="text-warning fa fa-print iconsize-item"></i>&ensp; In</a>
                                <a class="nts-color-excel dropdown-item textsize-item" href="javascript:void(0);"
                                    id="btnExport" onclick="previewExportv1('excel',tableHS);return false;"><i
                                        class="text-success fa fa-file-excel-o iconsize-item"></i>&ensp; Xuất
                                    Excel</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div style="display: flex; align-items: center" class="DivTimKiem">
                    <div class="input-icon">
                        <input type="text" value="" class="form-control nts-border-secondary"
                            placeholder="Nội dung tìm kiếm ..." id="SearchKey" autocomplete="off"
                            style="border-top-right-radius: 0px; border-bottom-right-radius: 0px; ">
                        <span class="input-icon-addon nts-secondary">
                            <i class="fa fa-search "></i>
                        </span>
                    </div>
                    <button id="TimKiemNangCao" class="btn btn-primary height-button-icon" type="button"
                        style="border-top-left-radius: 0px;border-bottom-left-radius:0px;">
                        <span class="fa fa-sliders" aria-hidden="true"> </span>
                    </button>
                </div>
                <div id="KhungTimKiem" class="bg-white mt-1 px-4 py-2 border rounded shadow position-absolute"
                    style="z-index: 99;width: 382px; display:none">
                    <div class="row">
                        <div class="col text-primary fw-bold">
                            <i class="fa fa-search"></i> Tìm kiếm nâng cao
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label" for="DonViID_Loc">Trường</label>
                            <select class="form-control input-sm" id="DonViID_Loc">
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label" for="TinhID_Loc">Tỉnh/Thành phố</label>
                            <select class="form-control input-sm" id="TinhID_Loc" tabindex="0">
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label" for="XaID_Loc">Phường/Xã</label>
                            <select class="form-control input-sm" id="XaID_Loc" tabindex="0">
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3 d-none">
                        <div class="col-md-12">
                            <label class="form-label" for="ThonID_Loc">Thôn/Ấp</label>
                            <select class="form-control input-sm" id="ThonID_Loc" tabindex="0">
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label" for="TrangThaiXuLy_Loc">Trạng thái</label>
                            <select class="form-control input-sm" id="TrangThaiXuLy_Loc" tabindex="0">

                            </select>
                        </div>
                    </div>
                    <hr style="margin:4px" />
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group" style="text-align:right">
                                <button type="button" id="DongTimKiem" class="btn btn-outline-danger"><i
                                        class="fa fa-times"></i>&ensp;Đóng</button>&ensp;
                                <button type="button" id="TimKiem" class="btn nts-color-them ms-auto"><i
                                        class="fa fa-search"></i>&ensp;Tìm kiếm</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row" style="margin-top: 4px" id="grid-layout">
            <input type="hidden" id="DoiTuongID" />
            <input type="hidden" id="DoiTuongID_View" />
            <div class="col-md-12">
                <div id="Grid1"></div>
            </div>
            <div class="col-md-12 d-none">
                <div id="tableHS"></div>
            </div>
        </div>
        <div class="row" id="list-layout" style="margin-top: 4px;display: none;">
            <div class="col-md-3" style="padding-right: 0">
                <div id="Grid2"></div>
            </div>
            <div class="col-md-9">
                <div class="info-container backdrop">
                    <div class="info-container-header">
                        <div class="info-container-header-left">
                            <div class="info-img-container">
                                <img class="info-img" id="avatar_View" src="{{ asset('img/user.png') }}" />
                            </div>
                            <div class="info-detail">
                                <span class="info-title" id="txtMaHocSinh_View">-</span> <br />
                                <span class="info-subtitle" id="txtTenHocSinh_View">-</span>
                            </div>
                        </div>
                        <div class="info-container-header-right">
                            <a href="#" id="btnSua2" class="btn btn-outline-light divThaoTacNhanh">
                                <i class="fa fa-pencil" aria-hidden="true"></i>&nbsp; Sửa
                            </a>
                            <div class="btn-group divThaoTacNhanh">
                                <div class="dropdown d-inline">
                                    <button class="btn btn-outline-light dropdown-toggle-hide-arrow height-button-icon"
                                        style="padding: 12px 16px;" type="button" data-bs-toggle="dropdown"
                                        aria-haspopup="true" aria-expanded="false">
                                        <i class="blue fa fa-ellipsis-h"></i>
                                    </button>
                                    <div class="dropdown-menu dropdown-menu-lg-end width-edit-hogiadinh">
                                        <a id="btnXem2" class="dropdown-item textsize-item"
                                            href="javascript:void(0);"><i
                                                class="fa fa-eye iconsize-item text-success"></i>&ensp; Xem học sinh</a>
                                        <a id="btnXoa2" class="dropdown-item textsize-item"
                                            href="javascript:void(0);"><i class='fa fa-trash-o text-danger'></i>&ensp;
                                            Xóa học sinh</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="info-body card">
                    <div class="card-header d-none">
                        <ul class="nav nav-tabs card-header-tabs" data-bs-toggle="tabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <a href="#Tab1" class="nav-link active" data-bs-toggle="tab" aria-selected="true"
                                    role="tab">Thông tin học sinh</a>
                            </li>
                            <li class="nav-item d-none" role="presentation">
                                <a href="#Tab2" class="nav-link" data-bs-toggle="tab" aria-selected="false"
                                    tabindex="-1" role="tab">Tab 2</a>
                            </li>

                            <li class="nav-item d-none" role="presentation">
                                <a href="#Tab3" class="nav-link" data-bs-toggle="tab" aria-selected="false"
                                    tabindex="-1" role="tab">Tab 3</a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content">
                            <div class="tab-pane fade active show" id="Tab1" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="label-container mb-2" style="display: flex;align-items: center">
                                            <label class="label-text" style="color: #f76707;"><b>Thông tin học
                                                    sinh</b></label>
                                            <div class="line"
                                                style="flex-grow: 2;border-bottom: 1px solid #dadcde;margin: 0 10px;border-color: #f76707;">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-bottom:4px">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <div class="col-md-12 control-div no-padding-right TieuDeLabel"> Mã học sinh:
                                                <span id="lblMaHocSinh_View"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-bottom:4px">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <div class="col-md-12 control-div no-padding-right TieuDeLabel"> Tên học sinh:
                                                <span id="lblTenHocSinh_View"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="form-group">
                                            <div class="col-md-12 control-div no-padding-right TieuDeLabel"> Giới tính:
                                                <span id="lblGioiTinh_View"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-bottom:4px">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <div class="col-md-12 control-div no-padding-right TieuDeLabel">Dân tộc: <span
                                                    id="lblDanToc_View"></span></div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <div class="col-md-12 control-div no-padding-right TieuDeLabel">Ngày sinh:
                                                <span id="lblNgaySinh_View"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <div class="col-md-12 control-div no-padding-right TieuDeLabel">Nơi sinh:
                                                <span id="lblNoiSinh_View"></span>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="row" style="margin-bottom:4px">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <div class="col-md-12 control-div no-padding-right TieuDeLabel">CCCD/CMT:
                                                <span id="lblCCCD_View"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <div class="col-md-12 control-div no-padding-right TieuDeLabel">Ngày cấp: <span
                                                    id="lblNgayCap_View"></span></div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <div class="col-md-12 control-div no-padding-right TieuDeLabel">Nơi cấp:
                                                <span id="lblNoiCap_View"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-bottom:4px">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <div class="col-md-12 control-div no-padding-right TieuDeLabel">Thuộc diện ưu
                                                tiên:
                                                <span id="lblThuocDienUuTien_View"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <div class="col-md-12 control-div no-padding-right TieuDeLabel">Số điện thoại:
                                                <span id="lblSDT_View"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <div class="col-md-12 control-div no-padding-right TieuDeLabel">Email: <span
                                                    id="lblEmail_View"></span></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-bottom:4px">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <div class="col-md-12 control-div no-padding-right TieuDeLabel">Địa chỉ:
                                                <span id="lblDiaChi_View"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="label-container mb-2" style="display: flex;align-items: center">
                                            <label class="label-text" style="color: #f76707;"><b>Thông tin học
                                                    tập</b></label>
                                            <div class="line"
                                                style="flex-grow: 2;
                                                                    border-bottom: 1px solid #dadcde;
                                                                    margin: 0 10px;
                                                                    border-color: #f76707;">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-bottom:4px">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <div class="col-md-12 control-div no-padding-right TieuDeLabel">
                                                Học sinh trường:
                                                <span id="lblTenDonVi_View"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-bottom:4px">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <div class="col-md-12 control-div no-padding-right TieuDeLabel">
                                                Học sinh lớp:
                                                <span id="lblTenLop_View"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-bottom:4px">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <div class="col-md-12 control-div no-padding-right TieuDeLabel">
                                                Ghi chú:
                                                <span id="lblGhiChu_View"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-bottom:4px">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <div class="col-md-12 control-div no-padding-right TieuDeLabel">
                                                Đính kèm:
                                                <span id="lblDinhKem_View"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-bottom:4px">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <div class="col-md-12 control-div no-padding-right TieuDeLabel"
                                                id="spTinhTrangTN_View">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-bottom:4px">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <div class="col-md-12 control-div no-padding-right TieuDeLabel"
                                                id="spTinhTrangCB_View">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="Tab2" role="tabpanel">
                                <span>Tab2</span>
                            </div>
                            <div class="tab-pane fade" id="Tab3" role="tabpanel">
                                <span>Tab3</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- Create Modal --}}
    <div class="modal modal-blur fade" id="mdThemMoi" tabindex="-1" aria-modal="true" role="dialog"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tieuDeModal"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row" style="margin: 5px;">
                        <div class="col-md-3 box-shadow-primary" style="border-radius: 6px;">
                            <fieldset class="KhungVien">
                                <legend>Thông tin học sinh</legend>
                                <div class="row mt-3">
                                    <div style="padding: 0 8px">
                                        <div class="centered"
                                            style=" display: flex; justify-content: center; align-items: center;">
                                            <div class="row mx-auto">
                                                <div class="col-12">
                                                    <span class="profile-picture" id="chonAvatar_DoiTuong">
                                                        <img id="avatar_DoiTuong"
                                                            class=" editable editable-click editable-empty img-fluid"
                                                            alt="Hình đại diện" src="{{ asset('img/user.png') }}">
                                                    </span>
                                                </div>
                                                <input type="file" class="d-none" id="avatar_us_DoiTuong"
                                                    accept=".jpg,.jpeg,.png">
                                            </div>
                                        </div>

                                        <div id="lblHovaten" style="text-align: center; font-weight: bold"
                                            class="text-primary">...</div>
                                        <div class="items-body">
                                            <span class="items-body-content">
                                                <span>Mã học sinh: <b id="lblMaHocSinh">...</b></span>
                                            </span>
                                            <span class="items-body-content">
                                                <span>Giới tính: <b id="lblGioiTinh">...</b></span>
                                            </span>
                                            <span class="items-body-content">
                                                <span>Dân tộc: <b id="lblDanToc">...</b></span>
                                            </span>
                                            <span class="items-body-content">
                                                <span>Ngày sinh: <b id="lblNgaySinh">...</b></span>
                                            </span>
                                            <span class="items-body-content">
                                                <span>Nơi sinh: <b id="lblNoiSinh">...</b></span>
                                            </span>
                                            <span class="items-body-content">
                                                <span>Địa chỉ: <b id="lblDiaChi">...</b></span>
                                            </span>
                                            <span class="items-body-content">
                                                <span>Học sinh trường: <b id="lblTruong">...</b></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                        <div class="col-md-9" style="padding-left: 10px">
                            <fieldset class="KhungVien">
                                <legend>Thông tin cá nhân</legend>
                                <div class="row">
                                    <div class="col-lg-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="MaDoiTuong">Mã học sinh</label>
                                            <input type="text" class="form-control" id="MaDoiTuong" required>
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="Hovaten">Họ và tên</label>
                                            <input type="text" class="form-control" id="Hovaten" required>
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="Gioitinh">Giới tính</label>
                                            <select class="form-control" id="Gioitinh" tabindex="0"
                                                data-dropdown-parent="#mdThemMoi"></select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="Ngaysinh">Ngày sinh</label>
                                            <input type="text" class="form-control date-picker" id="Ngaysinh"
                                                autocomplete="off" data-date-format="dd/mm/yyyy">
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="Noisinh">Nơi sinh</label>
                                            <input type="text" class="form-control" id="Noisinh">
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="DanTocID">Dân tộc</label>
                                            <select class="form-control" id="DanTocID" tabindex="0"
                                                data-dropdown-parent="#mdThemMoi">
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="CCCD">Số CMND/CCCD/Mã định danh</label>
                                            <input type="text" class="form-control" id="CCCD" required>
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="NgayCap">Ngày cấp</label>
                                            <input type="text" class="form-control date-picker" id="NgayCap"
                                                autocomplete="off" data-date-format="dd/mm/yyyy">
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="DiaBanHCID_NoiCap">Nơi cấp</label>
                                            <select class="form-control" id="DiaBanHCID_NoiCap" tabindex="0"
                                                data-dropdown-parent="#mdThemMoi">
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="DienUuTienID">Diện ưu tiên</label>
                                            <select class="form-control" id="DienUuTienID" tabindex="0"
                                                data-dropdown-parent="#mdThemMoi">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="SDT">Số điện thoại</label>
                                            <input type="text" class="form-control" id="SDT">
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="Email">Email</label>
                                            <input type="text" class="form-control" id="Email">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="SoNha">Số nhà</label>
                                            <input type="text" class="form-control" id="SoNha">
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="DiaBanHCID_Tinh">Tỉnh/thành phố</label>
                                            <select class="form-control" id="DiaBanHCID_Tinh" tabindex="0"
                                                data-dropdown-parent="#mdThemMoi">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="DiaBanHCID_Xa">Phường/xã</label>
                                            <select class="form-control" id="DiaBanHCID_Xa" tabindex="0"
                                                data-dropdown-parent="#mdThemMoi">
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-lg-4 d-none">
                                        <div class="mb-3">
                                            <label class="form-label" for="DiaBanHCID_Thon">Thôn/ấp</label>
                                            <select class="form-control" id="DiaBanHCID_Thon" tabindex="0"
                                                data-dropdown-parent="#mdThemMoi">
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="mb-3">
                                            <label class="form-label" for="DiaChi">Địa chỉ</label>
                                            <textarea class="form-control" id="DiaChi" rows="1"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                            <fieldset class="KhungVien">
                                <legend>Thông tin học tập</legend>
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="mb-3">
                                            <input type="hidden" id="DonViID_Hoc" />
                                            <label class="form-label" for="DonViID_Hoc">Tên trường</label>
                                            <div class="form-group" style="width: 100%;">
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <div class="form-group">
                                                            <div class="d-flex align-items-center">
                                                                <input class="form-control input-sm"
                                                                    style="border-top-right-radius: 0px !important;
                                                                    border-bottom-right-radius: 0px !important;"
                                                                    id="TenTruongHoc" disabled></input>
                                                                <button class="btn btn-sm btn-primary nts-color-luu"
                                                                    id="btnChonDonViCha"
                                                                    style=" height: 27px !important;border-top-left-radius: 0 !important; border-bottom-left-radius: 0 !important;">
                                                                    <i class="blue fa fa-ellipsis-h"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="LopHocID">Thuộc lớp</label>
                                            <select class="form-control" id="LopHocID" tabindex="0"
                                                data-dropdown-parent="#mdThemMoi">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-8">
                                        <div class="mb-3">
                                            <label class="form-label" for="GhiChu">Ghi chú</label>
                                            <textarea class="form-control" id="GhiChu" rows="1"></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        @include('partials.dinh-kem-v2', [
                                            'id' => 'DoiTuong',
                                        ])
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div style="display: contents; align-items: center; justify-content: space-between">
                        <div class="col-md-6" style="display: flex; align-items: center">
                            <label style="margin-bottom: unset" class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="TrangThai">
                                <label class="form-check-label" for="TrangThai">Đang sử dụng</label>
                            </label>
                        </div>
                        <div class="col-md-6">
                            <div style="float:right;text-align: right">
                                <a href="#" class="nts-color-dong btn btn-outline-danger" data-bs-dismiss="modal">
                                    <i class="fa fa-close"></i>&nbsp;Đóng (F4)
                                </a>
                                <a href="#" id="btnLuuVaDong" class="nts-color-luu btn btn-success ms-auto">
                                    <i class="fa fa-save"></i>&ensp;Lưu và đóng (F9)
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('layouts.ChonDonVi')
    @include('layouts.XemChiTietHocSinh')
@endsection

@push('scripts')
    <script>
        window.Laravel = window.Laravel || {}; // 👈 Bảo vệ trước khi gán
        window.Laravel.DoiTuong = {
            maTuTangUrl: `{{ route('dungchung.lay-ma-tutang', ['kyhieuLoaiPhieu' => 'MaDoiTuong', 'bangDuLieu' => 'doi_tuongs', 'cotDuLieu' => 'MaDoiTuong']) }}`,
            imgUser: `{{ asset('img/user.png') }}`,
            uploadFile: "{{ route('api.files.uploadFileTemp') }}",
            deleteFiles: "{{ route('api.files.deleteMultiple') }}",
            defaultAvatar: "{{ asset('img/user.png') }}",
            getListDienUuTien: "{{ route('dungchung.danhmuc.comboDienUuTien') }}",
            getListLopHoc: "{{ route('dungchung.danhmuc.comboLopHoc') }}",
            fileIconUrl: "{{ asset('') }}",
            getthongkedoituong: "{{ route('doituong.hs.getthongkedoituong') }}",
            getListTrangThai: "{{ route('doituong.hs.getListTrangThai') }}",
            thietlaphethong: "{{ route('thietlaphethong.getdata') }}",
        };
        // pre-generate the exact URL you want to call
        const maTuTangUrl =
            `{{ route('dungchung.lay-ma-tutang', ['kyhieuLoaiPhieu' => 'MaDoiTuong', 'bangDuLieu' => 'doi_tuongs', 'cotDuLieu' => 'MaDoiTuong']) }}`;
    </script>
    <script src="{{ asset('js/quanly/DoiTuong.js') }}?v={{ time() }}"></script>
@endpush
