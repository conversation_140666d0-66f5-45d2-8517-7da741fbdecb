<!-- Footer Component CSS -->
<link rel="stylesheet" href="{{ asset('css/congthongtin/footer.css') }}" type="text/css" media="all">

<footer class="footer-wrapper" role="contentinfo">
    <section class="footer-section">
        <div class="section-bg" aria-hidden="true"></div>
        <div class="section-content" style="position: relative; z-index: 2;">
            <div class="row">
                @if (isset($components['footer']) && $components['footer']->count() > 0)
                    @foreach ($components['footer'] as $component)
                        <div class="col {{ $component->TenComponent }} editable-component"
                            data-component-id="{{ $component->_id }}">
                            @if ($component->MauHTML)
                                {!! $component->MauHTML !!}
                            @else
                                @if ($component->TenComponent == 'cot-1')
                                    <div class="icon-box">
                                        <div class="icon-box-img">
                                            @if ($component->NoiDungText)
                                                @php $content = json_decode($component->NoiDungText, true); @endphp
                                                <img src="{{ $content['logoUrl'] ?? ($thietLapWebsite->LogoUrl ?? asset('assets/congthongtin/Seosona-Logo-1-300x116.png')) }}"
                                                    alt="{{ $content['companyName'] ?? ($thietLapWebsite->TenDonVi ?? 'Công ty TNHH Phát Triển Phần Mềm Nhất Tâm') }}"
                                                    loading="lazy">
                                            @else
                                                <img src="{{ $thietLapWebsite->LogoUrl ?? asset('assets/congthongtin/Seosona-Logo-1-300x116.png') }}"
                                                    alt="{{ $thietLapWebsite->TenDonVi ?? 'Công ty TNHH Phát Triển Phần Mềm Nhất Tâm' }}"
                                                    loading="lazy">
                                            @endif
                                        </div>
                                        <div class="icon-box-text">
                                            <p>{{ isset($thietLapWebsite) && $thietLapWebsite ? $thietLapWebsite->TenDonVi ?? 'Công ty TNHH Phát Triển Phần Mềm Nhất Tâm' : 'Công ty TNHH Phát Triển Phần Mềm Nhất Tâm' }}
                                                -
                                                {{ isset($thietLapWebsite) && $thietLapWebsite ? $thietLapWebsite->TenPhanMem ?? 'Hệ thống quản lý văn bằng chứng chỉ' : 'Hệ thống quản lý văn bằng chứng chỉ' }}
                                            </p>
                                        </div>
                                        <div class="social-icons" role="navigation" aria-label="Social Media Links">
                                            @if (isset($thietLapWebsite) && $thietLapWebsite)
                                                @if ($thietLapWebsite->Facebook && $thietLapWebsite->Facebook !== '#')
                                                    <a href="{{ $thietLapWebsite->Facebook }}" class="icon facebook"
                                                        target="_blank" rel="noopener noreferrer" aria-label="Facebook">
                                                        <i class="fab fa-facebook-f" aria-hidden="true"></i>
                                                    </a>
                                                @endif
                                                @if ($thietLapWebsite->Youtube && $thietLapWebsite->Youtube !== '#')
                                                    <a href="{{ $thietLapWebsite->Youtube }}" class="icon youtube"
                                                        target="_blank" rel="noopener noreferrer" aria-label="YouTube">
                                                        <i class="fab fa-youtube" aria-hidden="true"></i>
                                                    </a>
                                                @endif
                                                @if ($thietLapWebsite->Email)
                                                    <a href="mailto:{{ $thietLapWebsite->Email }}" class="icon email"
                                                        aria-label="Email">
                                                        <i class="fas fa-envelope" aria-hidden="true"></i>
                                                    </a>
                                                @endif

                                                @if ($thietLapWebsite->SoDienThoai)
                                                    <a href="tel:{{ str_replace(['(', ')', ' ', '-'], '', $thietLapWebsite->SoDienThoai) }}"
                                                        class="icon phone" aria-label="Phone">
                                                        <i class="fas fa-phone" aria-hidden="true"></i>
                                                    </a>
                                                @endif
                                            @else
                                                <a href="#" class="icon facebook" aria-label="Facebook"><i
                                                        class="fab fa-facebook-f" aria-hidden="true"></i></a>
                                                <a href="#" class="icon youtube" aria-label="YouTube"><i
                                                        class="fab fa-youtube" aria-hidden="true"></i></a>
                                                <a href="mailto:#" class="icon email" aria-label="Email"><i
                                                        class="fas fa-envelope" aria-hidden="true"></i></a>

                                                <a href="tel:#" class="icon phone" aria-label="Phone"><i
                                                        class="fas fa-phone" aria-hidden="true"></i></a>
                                            @endif
                                        </div>
                                    </div>
                                @elseif ($component->TenComponent == 'cot-2')
                                    <h3>
                                        @if ($component->NoiDungText)
                                            @php $content = json_decode($component->NoiDungText, true); @endphp
                                            {{ $content['title'] ?? 'Thông tin liên hệ' }}
                                        @else
                                            Thông tin liên hệ
                                        @endif
                                    </h3>
                                    <ul>
                                        @if ($component->NoiDungText)
                                            <li><i class="fa fa-map-marker"></i>
                                                {{ $content['address'] ?? 'Số H25, Đường Phan Văn Đáng, Phường 8, TP.Vĩnh Long, Tỉnh Vĩnh Long' }}
                                            </li>
                                            <li><i class="fa fa-phone"></i> Hotline:
                                                {{ $content['phone'] ?? '02703 843 058' }}</li>
                                            <li><i class="fa fa-envelope"></i>
                                                {{ $content['email'] ?? '<EMAIL>' }}</li>
                                        @else
                                            @if (isset($thietLapWebsite) && $thietLapWebsite)
                                                @if ($thietLapWebsite->DiaChi)
                                                    <li><i class="fa fa-map-marker"></i> {{ $thietLapWebsite->DiaChi }}
                                                    </li>
                                                @endif
                                                @if ($thietLapWebsite->SoDienThoai)
                                                    <li><i class="fa fa-phone"></i> Hotline:
                                                        {{ $thietLapWebsite->SoDienThoai }}</li>
                                                @endif
                                                @if ($thietLapWebsite->Email)
                                                    <li><i class="fa fa-envelope"></i> {{ $thietLapWebsite->Email }}
                                                    </li>
                                                @endif
                                            @else
                                                <li><i class="fa fa-map-marker"></i> Số H25, Đường Phan Văn Đáng, Phường
                                                    8, TP.Vĩnh Long, Tỉnh Vĩnh Long</li>
                                                <li><i class="fa fa-phone"></i> Hotline: 02703 843 058</li>
                                                <li><i class="fa fa-envelope"></i> <EMAIL></li>
                                            @endif
                                        @endif
                                    </ul>
                                @elseif ($component->TenComponent == 'cot-3')
                                    <h3>
                                        @if ($component->NoiDungText)
                                            @php $content = json_decode($component->NoiDungText, true); @endphp
                                            {{ $content['title'] ?? 'Các loại Tin tức' }}
                                        @else
                                            Các loại Tin tức
                                        @endif
                                    </h3>
                                    <ul>
                                        @if ($component->NoiDungText && isset($content['categories']))
                                            @foreach ($content['categories'] as $category)
                                                <li><a href="#">{{ $category }}</a></li>
                                            @endforeach
                                        @else
                                            @if (isset($loaiTinTucsMenu) && $loaiTinTucsMenu->count() > 0)
                                                @foreach ($loaiTinTucsMenu as $loaiTinTuc)
                                                    <li>
                                                        <a
                                                            href="{{ route('news.index', ['loai' => $loaiTinTuc->DinhDanh]) }}">
                                                            {{ $loaiTinTuc->TenLoaiTinTuc }}
                                                        </a>
                                                    </li>
                                                @endforeach
                                            @else
                                                <li><a href="{{ route('news.index') }}">Tất cả tin tức</a></li>
                                            @endif
                                        @endif
                                    </ul>
                                @endif
                            @endif
                        </div>
                    @endforeach
                @else
                    <div class="col cot-1 editable-component">
                        <div class="icon-box">
                            <div class="icon-box-img">
                                <img src="{{ isset($thietLapWebsite) && $thietLapWebsite && $thietLapWebsite->LogoUrl ? $thietLapWebsite->LogoUrl : asset('assets/congthongtin/Seosona-Logo-1-300x116.png') }}"
                                    alt="{{ isset($thietLapWebsite) && $thietLapWebsite ? $thietLapWebsite->TenDonVi ?? 'Company Logo' : 'Company Logo' }}" loading="lazy">
                            </div>
                            <div class="icon-box-text">
                                <p>Công ty TNHH Phát Triển Phần Mềm Nhất Tâm - Hệ thống quản lý văn bằng chứng chỉ</p>
                            </div>
                            <div class="social-icons" role="navigation" aria-label="Social Media Links">
                                <a href="#" class="icon facebook" aria-label="Facebook"><i
                                        class="fab fa-facebook-f"></i></a>
                                <a href="#" class="icon youtube" aria-label="YouTube"><i
                                        class="fab fa-youtube"></i></a>
                                <a href="mailto:#" class="icon email" aria-label="Email"><i
                                        class="fas fa-envelope"></i></a>
                                <a href="tel:#" class="icon phone" aria-label="Phone"><i
                                        class="fas fa-phone"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="col cot-2 editable-component">
                        <h3>Thông tin liên hệ</h3>
                        <ul>
                            <li><i class="fa fa-map-marker"></i> Số H25, Đường Phan Văn Đáng, Phường 8, TP.Vĩnh Long,
                                Tỉnh Vĩnh Long</li>
                            <li><i class="fa fa-phone"></i> Hotline: 02703 843 058</li>
                            <li><i class="fa fa-envelope"></i> <EMAIL></li>
                        </ul>
                    </div>
                    <div class="col cot-3 editable-component">
                        <h3>Các loại Tin tức</h3>
                        <ul>
                            @if (isset($loaiTinTucsMenu) && $loaiTinTucsMenu->count() > 0)
                                @foreach ($loaiTinTucsMenu as $loaiTinTuc)
                                    <li>
                                        <a href="{{ route('news.index', ['loai' => $loaiTinTuc->DinhDanh]) }}">
                                            {{ $loaiTinTuc->TenLoaiTinTuc }}
                                        </a>
                                    </li>
                                @endforeach
                            @else
                                <li><a href="{{ route('news.index') }}">Tất cả tin tức</a></li>
                            @endif
                        </ul>
                    </div>
                @endif
            </div>
            <div class="copyright-footer">
                @if (isset($thietLapWebsite) && $thietLapWebsite)
                    {{ $thietLapWebsite->TenPhienBan ?? 'Copyright © 2025' }}
                    {{ $thietLapWebsite->TenDonVi ?? 'Công ty TNHH Phát Triển Phần Mềm Nhất Tâm' }}. All rights
                    reserved.
                @else
                    Copyright © 2025 Công ty TNHH Phát Triển Phần Mềm Nhất Tâm. All rights reserved.
                @endif
            </div>
        </div>
    </section>
</footer>
