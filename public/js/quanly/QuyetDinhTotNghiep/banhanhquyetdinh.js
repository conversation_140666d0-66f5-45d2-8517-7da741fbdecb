// 1. Populate modal fields from an object
/**
 * Hàm điền thông tin vào modal ban hành quyết định tốt nghiệp.
 *
 * @param {string} tieuDe - Tiêu đề của modal.
 * @param {Object} data - <PERSON><PERSON> liệu liên quan đến thông tin ban hành.
 * @param {string} [data.NgayBanHanh] - <PERSON><PERSON><PERSON> ban hành quyết định.
 * @param {string} [data.NguoiBanHanh] - Người ban hành quyết định.
 * @param {string} [data.ChucVuBanHanh] - Chứ<PERSON> vụ của người ban hành.
 * @param {string} [data.NoiDungBanHanh] - Nội dung ban hành quyết định.
 * @param {Object} dataQuyetDinh - Dữ liệu liên quan đến quyết định tốt nghiệp.
 * @param {string} [dataQuyetDinh.SoQuyetDinh] - <PERSON><PERSON> quyết định tốt nghiệp.
 * @param {string} [dataQuyetDinh.CoQuanBanHanh] - <PERSON><PERSON> quan ban hành quyết định.
 * @param {string} [dataQuyetDinh.TrichYeu] - Trích yếu nội dung quyết định.
 *
 * @description Hàm này cập nhật các trường thông tin trong modal ban hành quyết định tốt nghiệp
 * và hiển thị thông báo hướng dẫn người dùng điền thông tin cần thiết để thực hiện thao tác ban hành.
 */
function dienMdBanHanh(tieuDe, data, dataQuyetDinh) {
    $("#ngayBanHanh").val(data?.NgayBanHanh || "");
    $("#nguoiBanHanh").val(data?.NguoiBanHanh || "");
    $("#chucVuBanHanh").val(data?.ChucVuBanHanh || "");
    $("#noiDungBanHanh").val(data?.NoiDungBanHanh || "");
    let textAlert = `
    Bạn đang thực hiện ban hành quyết định công nhận tốt nghiệp tại quyết định số:
                                <b>${
                                    dataQuyetDinh?.SoQuyetDinh
                                }</b> ngày ký <b>${$(
        "#ngayBanHanh"
    ).val()}</b> của <b>${dataQuyetDinh?.CoQuanBanHanh}</b> về việc ${
        dataQuyetDinh?.TrichYeu
    }. Vui
                                lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>“Ban hành”</b> để thực hiện
                                thao tác ban hành cho quyết định.
    `;
    // Optionally update alertMessage or fieldsetLegend if included in data
    $("#tieuDe_mdQuyetDinhTotNghiep").text(tieuDe);
    $("#alertMessage").html(textAlert);
}

// 2. Show the modal
function hienMdBanHanh() {
    $("#mdQuyetDinhTotNghiep").modal("show");
}

// 3. Clear all modal fields
function resetMdBanHanh() {
    $("#ngayBanHanh").val("");
    $("#nguoiBanHanh").val("");
    $("#chucVuBanHanh").val("");
    $("#noiDungBanHanh").val("");

    // Reset alertMessage and fieldsetLegend if needed
    $("#alertMessage").html(
        "Bạn đang thực hiện ban hành quyết định công nhận tốt nghiệp tại quyết định số: <b>01/2024/QĐ-SGD&ĐT</b> ngày ký <b>25/12/2024</b> của <b>Sở giáo dục và đào tạo Tỉnh NTSOFT</b> về việc công nhận học sinh sinh viên tốt nghiệp THPT năm học 2024. Vui lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>“Ban hành”</b> để thực hiện thao tác ban hành cho quyết định."
    );
    $("#fieldsetLegend").text("Thông tin ban hành");
}

// 4. Hide the modal
function anMdBanHanh() {
    const modalEl = document.getElementById("mdQuyetDinhTotNghiep");
    const modal = bootstrap.Modal.getInstance(modalEl);
    if (modal) modal.hide();
}
async function loadComboMdBanHanh() {
    loadDataCombos([
        // Load Chức vụ combo
        {
            name: "#chucVuBanHanh",
            ajaxUrl: Laravel.getListChucVu,
            columns: 2,
            indexValue: 0,
            indexText: 1,
            indexText1: 2,
            textShowTatCa: "-Chọn-",
            showTatCa: true,
        },
        // Load Nhân viên combo
        {
            name: "#nguoiBanHanh",
            ajaxUrl: Laravel.getListNhanvien,
            columns: 2,
            indexValue: 0,
            indexText: 1,
            indexText1: 2,
            textShowTatCa: "-Chọn-",
            showTatCa: true,
        },
    ]);
}

// Lây dữ liệu ban hành
function layDuLieuMdBanHanh() {
    return {
        NgayBanHanh: $("#ngayBanHanh").val().trim(),
        NguoiBanHanh: $("#nguoiBanHanh").val(),
        ChucVuBanHanh: $("#chucVuBanHanh").val(),
        NoiDungBanHanh: $("#noiDungBanHanh").val().trim(),
    };
}

//Tạo nội dung ban hành
function taoNoiDungBH(ngayKy, soQuyetDinh, coQuanBanHanh, trichYeu) {
    if (!ngayKy) ngayKy = "[Ngày ban hành]";
    if (!soQuyetDinh) soQuyetDinh = "";
    if (!coQuanBanHanh) coQuanBanHanh = "[Sở giáo dục và đào tạo]";
    if (!trichYeu) trichYeu = "[trích yếu]";

    return `Ban hành quyết định công nhận tốt nghiệp số: <b>${soQuyetDinh}</b> ngày ký <b>${ngayKy}</b> của <b>${coQuanBanHanh}</b> về việc <b>${trichYeu}</b>`;
}

function taoNoiDungTH(ngayKy, soQuyetDinh, coQuanBanHanh, trichYeu) {
    if (!ngayKy) ngayKy = "[Ngày ban hành]";
    if (!soQuyetDinh) soQuyetDinh = "";
    if (!coQuanBanHanh) coQuanBanHanh = "[Sở giáo dục và đào tạo]";
    if (!trichYeu) trichYeu = "[trích yếu]";

    return `Thu hồi quyết định công nhận tốt nghiệp số: <b>${soQuyetDinh}</b> ngày ký <b>${ngayKy}</b> của <b>${coQuanBanHanh}</b> về việc <b>${trichYeu}</b>`;
}

//#region Ban hành
//Show modal
function formatDateToDDMMYYYY(date) {
    const d = date.getDate().toString().padStart(2, "0");
    const m = (date.getMonth() + 1).toString().padStart(2, "0");
    const y = date.getFullYear();
    return `${d}/${m}/${y}`;
}

$(document).on("click", ".btnBanHanhQD", async function () {
    let id = $(this).data("id");
    selectedId = id;

    $("#btnBanHanh").html(`
        <i class="fa fa-calendar-check-o nts-iconThaoTacs" aria-hidden="true"></i>&nbsp;Ban hành (F9)`);
    loadComboMdBanHanh();

    let quyetDinhInfo = await NTS.getAjaxAPIAsync(
        "GET",
        Laravel.loadDuLieuSua,
        { id: id }
    );

    if (!quyetDinhInfo.Err) {
        debugger;
        let result = quyetDinhInfo.Result;

        dienMdBanHanh("Ban hành quyết định công nhận tốt nghiệp", null, result);

        const resultString = taoNoiDungBH(
            result.NgayKy,
            result.SoQuyetDinh,
            result.CoQuanBanHanh,
            result.TrichYeu
        );

        // Update somewhere on the page — for example the textarea
        $("#noiDungBanHanh").html(resultString);
    } else {
        NTS.loi(quyetDinhInfo.Msg);
    }

    $("#ngayBanHanh").val(formatDateToDDMMYYYY(new Date()));

    $("#fieldsetLegend").text("Thông tin ban hành");

    setLabelText(".form-label[for=ngayBanHanh]", "Ngày ban hành", true);
    setLabelText(".form-label[for=noiDungBanHanh]", "Nội dung ban hành", false);
    setLabelText(".form-label[for=nguoiBanHanh]", "Người ban hành", false);

    $("#mdQuyetDinhTotNghiep").modal("show");
});

//#region Thu hồi
$(document).on("click", ".btnThuHoiBanHanhQD", async function () {
    if (!QuyenSua()) return;

    let id = $(this).data("id");
    selectedId = id;
    $("#btnBanHanh").html(`
        <i class="fa fa-share-square-o nts-iconThaoTacs" aria-hidden="true"></i>&nbsp;Thu hồi (F9)`);
    loadComboMdBanHanh();

    let quyetDinhInfo = await NTS.getAjaxAPIAsync(
        "GET",
        Laravel.loadDuLieuSua,
        { id: id }
    );

    if (!quyetDinhInfo.Err) {
        let result = quyetDinhInfo.Result;

        // Populate modal with Thu hồi info if any or clear fields
        dienMdBanHanh(
            "Thu hồi ban hành quyết định công nhận tốt nghiệp",
            null,
            result
        );

        // Adjust alert message for thu hồi
        $("#alertMessage").html(`
            Bạn đang thực hiện thu hồi ban hành quyết định công nhận tốt nghiệp số:
            <b>${result.SoQuyetDinh}</b> ngày ký <b>${result.NgayKy}</b>.
            Vui lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>“Thu hồi”</b> để thực hiện thao tác.
        `);

        const resultString = taoNoiDungTH(
            result.NgayKy,
            result.SoQuyetDinh,
            result.CoQuanBanHanh,
            result.TrichYeu
        );

        // Update somewhere on the page — for example the textarea
        $("#noiDungBanHanh").html(resultString);
    } else {
        NTS.loi(quyetDinhInfo.Msg);
    }
    $("#fieldsetLegend").text("Thông tin thu hồi");
    $("#ngayBanHanh").val(formatDateToDDMMYYYY(new Date()));

    setLabelText(".form-label[for=ngayBanHanh]", "Ngày thu hồi", true);
    setLabelText(".form-label[for=noiDungBanHanh]", "Nội dung thu hồi", false);
    setLabelText(".form-label[for=nguoiBanHanh]", "Người thu hồi", false);

    $("#mdQuyetDinhTotNghiep").modal("show");
});

//region Lưu info thu hồi
$(document).on("click", "#btnBanHanh", async function () {
    if (!QuyenSua()) return;
    if (!selectedId) {
        NTS.loi("ID quyết định không hợp lệ");
        return;
    }

    // Detect if modal is in Thu hồi mode by title (or use a flag)
    const isThuHoi = $("#tieuDe_mdQuyetDinhTotNghiep")
        .text()
        .toLowerCase()
        .includes("thu hồi");

    const ngay = $("#ngayBanHanh").val().trim();
    if (!ngay) {
        NTS.loi("Vui lòng nhập Ngày ban hành");
        $("#ngayBanHanh").focus();
        return;
    }

    // Collect data (adjust keys for Thu hồi)
    const payload = isThuHoi
        ? {
              NgayThuHoi: $("#ngayBanHanh").val().trim(),
              NhanVienID_thuhoi: $("#nguoiBanHanh").val(),
              ChucVuQL_thuhoi: $("#chucVuBanHanh").val(),
              NoiDung_thuhoi: $("#noiDungBanHanh").html().trim(),
          }
        : {
              NgayBanHanh: $("#ngayBanHanh").val().trim(),
              NhanVienID: $("#nguoiBanHanh").val(),
              ChucVuQL_BH: $("#chucVuBanHanh").val(),
              NoiDungBH: $("#noiDungBanHanh").html().trim(),
          };

    const url = isThuHoi
        ? Laravel.luuThongTinThuHoi.replace("QUYETDINHID", selectedId)
        : Laravel.luuThongTinBanHanh.replace("QUYETDINHID", selectedId);

    try {
        const response = await NTS.getAjaxAPIAsync("PUT", url, payload);

        if (!response.Err) {
            NTS.thanhcong(
                isThuHoi
                    ? "Thu hồi quyết định thành công"
                    : "Ban hành quyết định thành công"
            );
            table.setData();
            anMdBanHanh();
        } else {
            NTS.loi(
                response.Msg ||
                    (isThuHoi
                        ? "Lỗi khi thu hồi"
                        : "Lỗi khi lưu thông tin ban hành")
            );
        }
    } catch (error) {
        console.error("Lỗi khi gọi API lưu thông tin:", error);
        NTS.loi("Lỗi hệ thống, vui lòng thử lại sau");
    }
});

//#region Shortcut
$(document).on("keydown", function (e) {
    const modalIsVisible = $("#mdQuyetDinhTotNghiep").hasClass("show");

    if (!modalIsVisible) return; // Only react if modal is open

    if (e.key === "Escape" || e.key === "F4") {
        e.preventDefault();
        anMdBanHanh();
    } else if (e.key === "F9") {
        e.preventDefault();
        $("#btnBanHanh").trigger("click");
    }
});
//#endregion

function setLabelText(selector, newText, keepSpan = false) {
    const $lbl = $(selector);

    if (keepSpan) {
        // grab the existing <span class="text-danger">…</span> if present
        const $star = $lbl.children("span.text-danger").detach();
        // remove all text nodes
        $lbl.contents()
            .filter((i, n) => n.nodeType === 3)
            .remove();
        // prepend the new text plus a space
        $lbl.prepend(newText + " ");
        // reattach the star
        $lbl.append($star);
    } else {
        // simple text replace (no span to keep)
        $lbl.text(newText);
    }
}
