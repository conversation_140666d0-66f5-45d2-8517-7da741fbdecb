var tempthem = "them";
var tempTrangThai = "40";
var ChuaCoThongTin = "";
///////// PHÍM TẮT /////////
var hotKey = 0; // 1 thêm\
var currentAvatarPath = "";
const today = new Date();
const dd = String(today.getDate()).padStart(2, '0');
const mm = String(today.getMonth() + 1).padStart(2, '0');
const yyyy = today.getFullYear();
const defaultDate = `${dd}/${mm}/${yyyy}`;
var selectedId;
$(function () {
    $(document).on("keydown", function (e) {
        switch (e.keyCode) {
            case 113:
                if (hotKey == 0) $("#btnThemMoi").trigger("click");
                e.preventDefault();
                break;
            case 114:
                if (hotKey == 0) $(".nav-search-input").focus();
                e.preventDefault();
                break;
            case 115:
                if (hotKey == 1) $("#mdThemMoi").modal("hide");
                e.preventDefault();
                break;
            case 120:
                if (hotKey == 1) $("#btnLuuVaDong").trigger("click");
                e.preventDefault();
                break;
        }
    });

    LoadDataComBo();
    LoadDataComBo_Loc();
    LoadDataTable();
    LoadDataComBo_GuiDonXinCapPhoiBang();
});

$(document).on("shown.bs.modal", "#mdThemMoi", function () {
    hotKey = 1;
});
$(document).on("hidden.bs.modal", "#mdThemMoi", function () {
    hotKey = 0;
});
$(document).on("click", "#btnThemMoi", function () {
    NTS.getAjaxAPIAsync("GET", window.Laravel.layouts.getCurrentUserInfo, {})
        .then((response) => {
            if (response.status && response.data) {
                const nhanVien = response.data.nhanVien;
                $("#NguoiLapID").value(nhanVien.id);
                if (nhanVien && nhanVien.chuc_vu) {
                    $("#ChucVuNguoiLapID").value(nhanVien.chuc_vu.id);
                }
            }
            console.log(response);
        })
        .catch((err) => {
            console.error("Lỗi khi lấy thông tin người dùng:", err);
        });
    ThemDuLieu();
});

async function ThemDuLieu() {
    if (!QuyenThem()) {
        return;
    }

    resetForm("#mdThemMoi");
    $("#tieuDeModal").text("Đơn yêu cầu chỉnh sửa nội dung văn bằng, chứng chỉ");
    $("#YeuCauCSNDVBCC").val("");
    tempthem = "them";
    try {
        const input = document.getElementById("NgayLap");
        const today = new Date();
        const dd = String(today.getDate()).padStart(2, '0');
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const yyyy = today.getFullYear();

        input.value = `${dd}/${mm}/${yyyy}`;
        document.getElementById("NgayLap").value = `${dd}/${mm}/${yyyy}`;
    } catch { }

    $("#mdThemMoi").modal("show");

}

async function SuaDuLieu(id) {
    if (!QuyenSua()) {
        return false;
    }

    $("#tieuDeModal").text("Cập nhật đơn yêu cầu chỉnh sửa văn bằng");
    $("#YeuCauCSNDVBCCID").val(id);

    const result = await NTS.getAjaxAPIAsync("GET", window.location.pathname + "/loadDuLieuSua", { id: id });

    if (!result.Err) {
        const data = result.Result;

        $("#NguoiLapID").value(data.NguoiLapID || "");
        $("#ChucVuNguoiLapID").value(data.ChucVuNguoiLapID || "");
        $("#DonViNhanID").value(data.DonViNhanID || "");
        $("#LyDoDieuChinh").val(data.LyDoDieuChinh || "");
        $("#GhiChu").val(data.GhiChu || "");
        $("#SoVaoSo").val(data.SoVaoSo || "");
        $("#SoHieu").val(data.SoHieu || "");
        $("#LoaiVBCCID").value(data.LoaiVBCCID || "");
        $("#NgayLap").value(data.NgayLap || "");
        $("#NgayCap").value(data.NgayCap || "");
        $('#HocSinhID').value(data.HocSinhID || "");

        if (data.DinhKem) {
            $("#dinhKemQD_txtDuongDanFileVB").val(data.DinhKem);
            $("#dinhKemQD_list-file").empty();

            var links = data.DinhKem.split('|');
            links.forEach(function (link) {
                link = link.trim();
                if (link) {
                    renderAttachment(link);
                }
            });
        }
        let phoiVBCCArr = [];
        if (typeof data.PhoiVBCC === "string") {
            try {
                phoiVBCCArr = JSON.parse(data.PhoiVBCC);
            } catch {
                phoiVBCCArr = [];
            }
        } else if (Array.isArray(data.PhoiVBCC)) {
            phoiVBCCArr = data.PhoiVBCC;
        }

        try {
            await initPhoiVBCCTable(phoiVBCCArr);
        } catch (error) {
            console.error("Lỗi khi khởi tạo bảng phôi VBCC:", error);
        }
        $("#mdThemMoi").modal("show");
        tempthem = "sua";
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

function GuiDonXinCapPhoiBang(id, txtNgayLap, DonViGui) {
    $("#YeuCauCSNDVBCCID").val(id);
    $("#NgayXuLy").val(defaultDate);

    NTS.getAjaxAPIAsync("GET", window.Laravel.layouts.getCurrentUserInfo, {})
        .then((response) => {
            if (response.status && response.data) {
                const nhanVien = response.data.nhanVien;
                $("#NguoiXuLyID").value(nhanVien.id);
                if (nhanVien && nhanVien.chuc_vu) {
                    $("#ChucVuNguoiXuLyID").value(nhanVien.chuc_vu.id);
                }
            }
        })
        .catch((err) => {
            console.error("Lỗi khi lấy thông tin người dùng:", err);
        });

    $("#tieuDe_mdGuiDonXinCapPhoiBang").text("Gửi đơn yêu cầu chỉnh sửa văn bằng, chứng chỉ");
    $("#alertMessage").html('Bạn đang thực hiện gửi đơn yêu cầu chỉnh sửa văn bằng, chứng chỉ, ngày lập <b id="txtNgayLap_lbl"></b> của <b id="TenDonViGui_lbl"></b>, Vui lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>"Gửi"</b> để thực hiện thao tác gửi đơn yêu cầu chỉnh sửa văn bằng, chứng chỉ đến cơ quan có thẩm quyền.');
    $("#btnGuiDon").html('<i class="fa fa-paper-plane-o me-1"></i> Gửi (F9)');
    $('#txtNgayLap_lbl').text(txtNgayLap)
    $('#TenDonViGui_lbl').text(DonViGui)
    tempTrangThai = "40";
    $('#DonViTiepNhanXuLyID').closest('.col-12').show();
    $('#DonViTiepNhanXuLyID').attr('required', true);
    $('#mdGuiDonXinCapPhoiBang').modal('show');

    const noiDungText = `Bạn đang thực hiện gửi đơn yêu cầu chỉnh sửa văn bằng, chứng chỉ ngày lập ${txtNgayLap} của ${DonViGui}. Vui lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút "Gửi" để thực hiện thao tác gửi đơn yêu cầu đến cơ quan có thẩm quyền.`;
    $('#NoiDungXuLy').val(noiDungText);
}
function ThuHoiDonXinCapPhoiBang(id, txtNgayLap, DonViGui) {
    $("#YeuCauCSNDVBCCID").val(id);
    $("#NgayXuLy").val(defaultDate);

    NTS.getAjaxAPIAsync("GET", window.Laravel.layouts.getCurrentUserInfo, {})
        .then((response) => {
            if (response.status && response.data) {
                const nhanVien = response.data.nhanVien;
                $("#NguoiXuLyID").value(nhanVien.id);
                if (nhanVien && nhanVien.chuc_vu) {
                    $("#ChucVuNguoiXuLyID").value(nhanVien.chuc_vu.id);
                }
            }
            console.log(response);
        })
        .catch((err) => {
            console.error("Lỗi khi lấy thông tin người dùng:", err);
        });

    $("#tieuDe_mdGuiDonXinCapPhoiBang").text("Thu hồi gửi phiếu đề nghị cấp phôi văn bằng, chứng chỉ");
    $("#alertMessage").html('Bạn đang thực hiện thu hồi gửi đơn yêu cầu chỉnh sửa văn bằng, chứng chỉ, ngày lập <b id="txtNgayLap_lbl"></b> của <b id="TenDonViGui_lbl"></b>, Vui lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>"Thu hồi"</b> để thực hiện thao tác thu hồi đơn yêu cầu đến cơ quan có thẩm quyền.');
    $("#btnGuiDon").html('<i class="fa fa-share-square-o nts-iconThaoTacs" aria-hidden="true"></i>&ensp;Thu hồi (F9)');
    $('#txtNgayLap_lbl').text(txtNgayLap)
    $('#TenDonViGui_lbl').text(DonViGui)
    tempTrangThai = "41";
    $('#DonViTiepNhanXuLyID').closest('.col-12').hide();
    $('#DonViTiepNhanXuLyID').removeAttr('required');
    $('#mdGuiDonXinCapPhoiBang').modal('show');
}
function XemChiTietNhatKy(id) {
    $("#YeuCauCSNDVBCCID").val(id);
    selectedId = id;
    $.ajax({
        url: window.location.pathname + '/loadDuLieuSua',
        method: 'GET',
        data: { id: id },
        success: function (res) {
            const configByTrangThai = {
                40: {
                    labelNgay: 'Ngày đề nghị    ',
                    labelNguoi: 'Người đề nghị',
                    labelChucVu: 'Chức vụ người đề nghị',
                    labelNoiDung: 'Nội dung đề nghị',

                },
                41: {
                    labelNgay: 'Ngày gửi',
                    labelNguoi: 'Người gửi',
                    labelChucVu: 'Chức vụ người gửi',
                    labelNoiDung: 'Nội dung gửi',

                },
                42: {
                    labelNgay: 'Ngày từ chối',
                    labelNguoi: 'Người từ chối',
                    labelChucVu: 'Chức vụ người từ chối',
                    labelNoiDung: 'Lý do từ chối',

                },
                default: {
                    labelNgay: 'Ngày xử lý',
                    labelNguoi: 'Người xử lý',
                    labelChucVu: 'Chức vụ',
                    labelNoiDung: 'Nội dung',

                }
            };
            if (!res.Err && res.Result) {
                var data = res.Result;
                if (window.Laravel && window.Laravel.local && window.Laravel.local.linkAnhDonXin) {
                    $('#dxcpb_imgDonXin').attr('src', window.Laravel.local.linkAnhDonXin);
                }
                $('#dxcpb_NgayLap_ct').text(data.txtNgayLap || '');
                $('#dxcpb_NguoiLap_ct').text(data.TenNguoiLap || '');
                $('#dxcpb_ChucVuNguoiLap_ct').text(data.TenChucVuNguoiLap || '');
                $('#dxcpb_TenHocSinh_ct').text(data.TenHocSinh || '');
                $('#dxcpb_DonViGui_ct').text(data.TenDonViGui || '');
                $('#dxcpb_LyDoDieuChinh_ct').text(data.LyDoDieuChinh || '');
                $('#dxcpb_SoHieu_ct').text(data.SoHieu || '');
                $('#dxcpb_SoVaoSo_ct').text(data.SoVaoSo || '');
                $('#dxcpb_DonViNhan_ct').text((data.TenDonViNhan || '') + (data.MaDonViNhan ? ` (${data.MaDonViNhan})` : ''));
                $('#dxcpb_LyDoXinCap_ct').text(data.LyDoXinCap || '');
                $('#dxcpb_TrangThai_ct')
                    .text(data.TenTrangThaiXuLy || 'Chưa rõ')
                    .css('background-color', data.MauSacTrangThaiXuLy || '#ccc');

                $('#txtDinhKem').html(`<p class="fs-big my-1">
            Đính kèm:
            <a href="#" data="" onclick="XemDinhKem_us('`+ data.DinhKem + `')">
                <i class="fa fa-paperclip me-1"></i> Xem đính kèm
            </a>
                            </p> `)

                var labelNgay = 'Ngày xử lý', labelNguoi = 'Người xử lý', labelChucVu = 'Chức vụ', labelNoiDung = 'Nội dung';
                if (data.TrangThaiXuLyID == 40) {
                    labelNgay = 'Ngày đề nghị';
                    labelNguoi = 'Người đề nghị';
                    labelChucVu = 'Chức vụ người đề nghị';
                    labelNoiDung = 'Nội dung đề nghị';
                    labelDonViTiepNhan = 'Đơn vị tiếp nhận';
                } else if (data.TrangThaiXuLyID == 41) {
                    labelNgay = 'Ngày gửi';
                    labelNguoi = 'Người gửi';
                    labelChucVu = 'Chức vụ người gửi';
                    labelNoiDung = 'Nội dung gửi';
                } else if (data.TrangThaiXuLyID == 42) {
                    labelNgay = 'Ngày từ chối';
                    labelNguoi = 'Người từ chối';
                    labelChucVu = 'Chức vụ người từ chối';
                    labelNoiDung = 'Lý do từ chối';
                }

                const config = configByTrangThai[parseInt(data.TrangThaiXuLyID)] || configByTrangThai.default;

                $('#dxcpb_LabelNgay_ct').html('<b>' + (data.txtNgayXuLy || '') + '</b>');
                $('#dxcpb_LabelNguoi_ct').html('<b>' + (data.TenNguoiXuLy || '') + '</b>');
                $('#dxcpb_LabelChucVu_ct').html('<b>' + (data.TenChucVuNguoiXuLy || '') + '</b>');
                $('#dxcpb_LabelNoiDung_ct').html('<b>' + (data.NoiDungXuLy || '') + '</b>');

                $('#dxcpb_LabelNgayTiepNhanXuLy_ct').text(config.labelNgay + ': ');
                $('#dxcpb_NgayTiepNhanXuLy_ct').text(data.txtNgayTiepNhanXuLy || '');

                $('#dxcpb_LabelNguoiTiepNhanXuLy_ct').text(config.labelNguoi + ': ');
                $('#dxcpb_NguoiTiepNhanXuLy_ct').text(data.TenNguoiTiepNhanXuLy || '');

                $('#dxcpb_LabelChucVuNguoiTiepNhanXuLy_ct').text(config.labelChucVu + ': ');
                $('#dxcpb_ChucVuNguoiTiepNhanXuLy_ct').text(data.TenChucVuNguoiTiepNhanXuLy || '');

                $('#dxcpb_LabelNoiDungTiepNhan_ct').text(config.labelNoiDung + ': ');
                $('#dxcpb_NoiDungTiepNhanXuLy_ct').text(data.NoiDungTiepNhanXuLy || '');

                $('#dxcpb_DonViTiepNhanXuLyID_ct').text(data.TenDonViTiepNhanXuLy || '');

            } else {
                $('#dxcpb_imgDonXin').attr('src', window.Laravel && window.Laravel.local ? window.Laravel.local.linkAnhDonXin : '');
                $('#dxcpb_TrangThaiLabel').text('').css('background-color', '#ccc');
                $('#dxcpb_NgayLap_ct, #dxcpb_NguoiLap_ct, #dxcpb_ChucVuNguoiLap_ct, #dxcpb_DonViGui_ct, #dxcpb_LoaiPhoi_ct, #dxcpb_SoLuong_ct, #dxcpb_DonViNhan_ct, #dxcpb_LyDoXinCap_ct, #dxcpb_DinhKem_ct, #dxcpb_GhiChu_ct, #dxcpb_LabelNgay_ct, #dxcpb_LabelNguoi_ct, #dxcpb_LabelChucVu_ct, #dxcpb_LabelNoiDung_ct').text('');
            }
            $('#mdChiTietQD').modal('show');
        },
        error: function () {
            $('#dxcpb_imgDonXin').attr('src', window.Laravel && window.Laravel.local ? window.Laravel.local.linkAnhDonXin : '');
            $('#dxcpb_TrangThaiLabel').text('').css('background-color', '#ccc');
            $('#dxcpb_NgayLap_ct, #dxcpb_NguoiLap_ct, #dxcpb_ChucVuNguoiLap_ct, #dxcpb_DonViGui_ct, #dxcpb_LoaiPhoi_ct, #dxcpb_SoLuong_ct, #dxcpb_DonViNhan_ct, #dxcpb_LyDoXinCap_ct, #dxcpb_DinhKem_ct, #dxcpb_GhiChu_ct, #dxcpb_LabelNgay_ct, #dxcpb_LabelNguoi_ct, #dxcpb_LabelChucVu_ct, #dxcpb_LabelNoiDung_ct').text('');
            $('#mdChiTietQD').modal('show');
        }
    });
}

async function XoaDuLieu(ID) {
    var result_ktxoa = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.layouts.KiemTraXoa,
        {
            ma: ID,
            model: "QuanLy\\YeuCauCSNDVBCC",
        }
    );

    if (!result_ktxoa.Err) {
        CanhBaoXoa(async () => {
            var result = await NTS.getAjaxAPIAsync(
                "DELETE",
                window.location.pathname + "/xoa",
                { ma: ID }
            );
            if (!result.Err) {
                LoadDataTable();
                NTS.thanhcong(result.Msg);
            } else {
                result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
            }
        });
        return false;
    } else {
        NTS.canhbao(result_ktxoa.Msg);
        return false;
    }
}

let commonComboConfig = {
    columns: 2,
    indexValue: 0,
    indexText: 1, // assuming your result rows are [id, code, name]
    indexText1: 2, // assuming your result rows are [id, code, name]
    textShowTatCa: "-Chọn-",
    showTatCa: true,
};

function LoadDataComBo() {
    NTS.loadDataComboAsync({
        name: "#NguoiLapID",
        type: "GET",
        ajaxUrl: window.Laravel.local.comboNhanVien,
        ajaxParam: {},
        ...commonComboConfig
    });
    NTS.loadDataComboAsync({
        name: "#ChucVuNguoiLapID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListChucVu,
        ajaxParam: {},
        ...commonComboConfig
    });
    NTS.loadDataComboAsync({
        name: "#LoaiVBCCID",
        type: "POST",
        ajaxUrl: window.Laravel.local.getloaiphoivanbangchungchi,
        ajaxParam: {},
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: true,
    });
    NTS.loadDataComboAsync({
        name: "#DonViNhanID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListDonVi,
        ajaxParam: {},
        ...commonComboConfig
    });
}

function LoadDataComBo_Loc() {
    NTS.loadDataComboAsync({
        name: "#LoaiPhoiID_Loc",
        type: "POST",
        ajaxUrl: window.Laravel.local.getloaiphoivanbangchungchi,
        ajaxParam: {},
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Tất cả-",
        showTatCa: true,
    });

    NTS.loadDataComboAsync({
        name: "#TrangThaiXuLyID_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListTrangThaiCapBang,
        ajaxParam: {},
        ...commonComboConfig,
        indexValue: 1,
        textShowTatCa: "-Tất cả-",

    });
}

function LoadDataComBo_GuiDonXinCapPhoiBang() {
    NTS.loadDataComboAsync({
        name: "#NguoiXuLyID",
        type: "GET",
        ajaxUrl: window.Laravel.local.comboNhanVien,
        ajaxParam: {},
        ...commonComboConfig
    });
    NTS.loadDataComboAsync({
        name: "#ChucVuNguoiXuLyID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListChucVu,
        ajaxParam: {},
        ...commonComboConfig
    });
    NTS.loadDataComboAsync({
        name: "#DonViTiepNhanXuLyID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListDonVi,
        ajaxParam: {},
        ...commonComboConfig
    });
}

$(document).on("click", "#TimKiemNangCao", function () {
    if ($("#KhungTimKiem").css("display") == "block") {
        $("#KhungTimKiem").slideUp(200);
    } else {
        $("#KhungTimKiem").slideDown(200);
    }
    return false;
});
$(document).on("click", "#DongTimKiem", function () {
    $("#KhungTimKiem").slideUp(200);
    return false;
});

$(document).on("click", "#btnAnHienTQ", function () {
    var divThongKe = document.getElementById("DivThongKe");
    var textAnHien = document.getElementById("textAnHien");
    var iconAnHien = document.getElementById("iconAnHien");
    if (iconAnHien.className == "fa fa-eye-slash") {
        divThongKe.classList.add("d-none");
        iconAnHien.className = "fa fa-eye";
        textAnHien.textContent = " Hiện trang tổng quan";
    } else {
        divThongKe.classList.remove("d-none");
        iconAnHien.className = "fa fa-eye-slash";
        textAnHien.textContent = " Ẩn trang tổng quan";
    }
});

$("#btnLuuVaDong").on("click", async function () {
    const validate = new NTSValidate("#mdThemMoi");
    if (!validate.trim().check()) return false;

    const dinhKemQDValue = $("#dinhKemQD").val();

    const payload = {
        NgayLap: $("#NgayLap").val(),
        NguoiLapID: $("#NguoiLapID").val(),
        ChucVuNguoiLapID: $("#ChucVuNguoiLapID").val(),
        HocSinhID: $("#HocSinhID").val(),
        LoaiVBCCID: $("#LoaiVBCCID").val(),
        DonViNhanID: $("#DonViNhanID").val(),
        NgayCap: $("#NgayCap").val(),
        SoHieu: $("#SoHieu").val(),
        SoVaoSo: $("#SoVaoSo").val(),
        LyDoDieuChinh: $("#LyDoDieuChinh").val(),
        GhiChu: $("#GhiChu").val(),
        YeuCauCSNDVBCCID: $("#YeuCauCSNDVBCCID").value(),
        DinhKem: dinhKemQDValue,
    };
    var met = "POST";
    if (tempthem == "them") {
        met = "POST";
    } else {
        met = "PUT";
    }
    var result = await NTS.getAjaxAPIAsync(
        met,
        window.location.pathname + "/luuThongTin",
        payload
    );
    if (!result.Err) {
        NTS.thanhcong(result.Msg);
        LoadDataTable();
        $("#mdThemMoi").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});

$(document).on("keyup", "#timKiemTruongHoc", async function (e) {
    if (e.keyCode == '13') {
        gridTruongHoc.setFilter(matchAny, { value: $(this).val() });
    }
});

var gridTruongHoc = new Tabulator("#gridTruongHoc", {
    height: "420px",
    layout: "fitColumns",
    placeholder: "Không có dữ liệu trường học",
    headerVisible: false,
    selectable: true,
    columns: [
        {
            field: "action",
            width: "30",
            formatter: function () {
                return '<div class="cell-center"><i class="fas fa-hand-point-right text-nts-primary"></i> </div>';
            },
            hozAlign: "center"
        },
        {
            title: "Tên trường",
            cssClass: "cell-wrap",

            field: "name", // use 'name' since that's your data key
            formatter: function (cell) {
                const data = cell.getData();
                const name = data.name || "";
                const count = data.count || 0;

                // Badge style using your --main-color (rgb(247 109 35))
                const badgeStyle = `
                    background-color: rgb(247, 109, 35);
                    color: white;
                    border-radius: 3px;
                    padding: 0.25em 0.6em;
                    font-size: 0.75rem;
                    font-weight: 600;
                    margin-left: 8px;
                    display: inline-block;
                    min-width: 22px;
                    text-align: center;
                    line-height: 1;
                    vertical-align: middle;
                `;

                return `
                    <span style="  white-space: normal;">${name}</span>

                `;
            },
        },
    ],
    rowClick: function (e, row) {
        // Load students for clicked school row
        var donViId = row.getData().id;
        loadStudentsByDonViId(donViId);
    },
});

$(document).on("keyup", "#timKiemHocSinh", async function (e) {

    if (e.keyCode == '13') {
        gridChonHocSinh.setFilter(matchAny, { value: $(this).val() });
    }
});

var gridChonHocSinh = new Tabulator("#gridChonHocSinh", {
    height: "420px",
    layout: "fitColumns",
    placeholder: "Không có học sinh được chọn",
    selectable: false,
    columns: [
        {
            formatter: "rowSelection",
            hozAlign: "center",
            headerSort: false,
            width: 50,
            selectable: true,
            cellClick: function (e, cell) {
                cell.getRow().toggleSelect();
            },
        },
        {
            title: "Mã học sinh",
            field: "MaDoiTuong",
            width: 120,
        },
        {
            title: "Họ và tên",
            field: "Hovaten",
            width: 150,
            formatter: "textarea",
        },
        {
            title: "Số CMND/CCCD",
            field: "CCCD",
            width: 150,
        },
        {
            title: "Ngày sinh",
            field: "Ngaysinh",
            sorter: "date",
            hozAlign: "center",
            width: 120,
            formatter: function (cell) {
                const val = cell.getValue();
                if (!val) return "";
                const date = new Date(val);
                return date.toLocaleDateString(); // format as needed
            },
        },
        {
            title: "Giới tính",
            field: "Gioitinh",
            hozAlign: "center",
            width: 100,
        },
        {
            title: "Dân tộc",
            field: "dan_toc.tenDanToc", // Use nested related field here
            width: 120,
        },
        {
            title: "Nơi sinh",
            field: "Noisinh",
            width: 150,
        },
        {
            title: "Lớp học",
            field: "lopHoc", // Note: make sure API returns this field, or remove if not available
            width: 120,
        },
        {
            title: "Địa chỉ",
            field: "DiaChi",
            width: 200,
        },
        {
            title: "Đơn vị học",
            field: "don_vi_hoc.TenDonVi", // nested related field
            width: 200,
            formatter: function (cell) {
                return cell.getValue() || "";
            },
        },
        {
            title: "Địa bàn tỉnh",
            field: "dia_ban_tinh.TenDiaBan", // nested related field
            minWidth: 200,
            formatter: function (cell) {
                return cell.getValue() || "";
            },
        },
    ],
});
$("#btnChonDonViCha").on("click", async function () {

    $('#mdChonHocSinh').modal('show')
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.local.getListTruongHoc,
        {}
    );
    if (!result.Err) {
        gridTruongHoc.setData(result.Result);
    } else {
        gridTruongHoc.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
});
gridTruongHoc.on("rowClick", async (e, row) => {
    // Load students for clicked school row
    let donViId = row.getData().id;
    selectedTruongHoc = row.getData();

    loadStudentsByDonViId(donViId);
});
function loadStudentsByDonViId(donViId) {
    const url = window.Laravel.local.getListHS(donViId);
    NTS.getAjaxAPIAsync("GET", url, {})
        .then((res) => {
            //Xử lý kết quả trả về
            if (!res.Err) {
                gridChonHocSinh.setData(res.Result);
            } else {
                NTS.loi("Lỗi khi tải danh sách học sinh");
                gridChonHocSinh.clearData();
            }
        })
        .catch(() => {
            NTS.loi("Lỗi khi tải danh sách học sinh");
            gridChonHocSinh.clearData();
        });
}

$(document).on("click", "#btnChonVaDong", async function () {
    let selectedRows = gridChonHocSinh.getSelectedData();
    if (selectedRows.length === 0) {
        NTS.canhbao("Vui lòng chọn ít nhất một học sinh.");
        return;
    }
    $('#HocSinhID').value(selectedRows[0].id)
    $('#TenTruongHoc').value(selectedRows[0].MaDoiTuong + ": " + selectedRows[0].Hovaten + " - " + selectedRows[0].don_vi_hoc.TenDonVi)
    $("#mdChonHocSinh").modal("hide");
});

function htmlDuLieu(cell) {
    const data = cell.getData();

    const fileName = data.TenTepDinhKem || "Không rõ";
    const fileLink = data.LinkDinhKem || "#";
    const dinhKemHTML = data.LinkDinhKem
        ? `<a href="${fileLink}" target="_blank">${fileName}</a>`
        : "Không có";

    let labelNgay, labelNguoi, labelChucVu, labelNoiDung;
    let txtNgay = data.txtNgayTiepNhanXuLy || "";
    let tenNguoi = data.TenNguoiTiepNhanXuLy || "";
    let chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
    let noiDung = data.NoiDungTiepNhanXuLy || "";

    switch (parseInt(data.TrangThaiXuLyID)) {
        case 40:
            labelNgay = "Ngày gửi";
            labelNguoi = "Người gửi";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung gửi";

            txtNgay = data.txtNgayXuLy || "";
            tenNguoi = data.TenNguoiXuLy || "";
            chucVu = data.TenChucVuNguoiXuLy || "";
            noiDung = data.NoiDungXuLy || "";
            break;
        case 41:
            labelNgay = "Ngày yêu cầu";
            labelNguoi = "Người yêu cầu";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung yêu cầu";

            txtNgay = data.txtNgayTiepNhanXuLy || "";
            tenNguoi = data.TenNguoiTiepNhanXuLy || "";
            chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
            noiDung = data.NoiDungTiepNhanXuLy || "";
            break;
        case 42:
            labelNgay = "Ngày từ chối";
            labelNguoi = "Người từ chối";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung từ chối";


            txtNgay = data.txtNgayTiepNhanXuLy || "";
            tenNguoi = data.TenNguoiTiepNhanXuLy || "";
            chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
            noiDung = data.NoiDungTiepNhanXuLy || "";
            break;
        case 32:
            labelNgay = "Ngày duyệt";
            labelNguoi = "Người duyệt";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung duyệt";


            txtNgay = data.txtNgayTiepNhanXuLy || "";
            tenNguoi = data.TenNguoiTiepNhanXuLy || "";
            chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
            noiDung = data.NoiDungTiepNhanXuLy || "";
            break;
        default:
            labelNgay = "Ngày yêu cầu";
            labelNguoi = "Người yêu cầu";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung yêu cầu";

            txtNgay = data.txtNgayTiepNhanXuLy || "";
            tenNguoi = data.TenNguoiTiepNhanXuLy || "";
            chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
            noiDung = data.NoiDungTiepNhanXuLy || "";
            break;

    }
    return `<div class="list-item col-md-12" style="padding: 0px;">
        <div class="card card-luoi shadow-sm mb-2">
            <div id="card_${data.id}" class="card-body profile-user-box">
                <div class="row">
                    <div class="col-sm-2 text-center" style="width:12%;">
                        <div class="profile-picture" style=" height: 109px;">
                            <img src="${window.Laravel.local.linkAnhDonXin}" alt="ảnh đơn xin" class="img-thumbnail rounded lazy mb-2" style="background: white;
                            border: none;
                            box-shadow: unset;">
                        </div>
                        <div style="display: flex; justify-content: center;">
                            <span style="
                                display: block;
                                margin-top: 10px;
                                font-weight: bold;
                                font-size: 1rem;
                                text-align: center;
                                padding: 6px 0;
                                background-color: ${data.MauSacTrangThaiXuLy};
                                color: white;
                                border-radius: 6px;
                                width: 95%;
                                ">
                                ${data.TenTrangThaiXuLy}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-10" style="width:88%;">
                        <div class="row">
                            <div class="col-md-11">
                                <div class="row mb-2">
                                    <div class="col-md-4">
                                        <p class="fs-big my-1">Ngày lập: <b>${data.txtNgayLap || ""}</b></p>
                                    </div>
                                    <div class="col-md-4">
                                        <p class="fs-big my-1">Người lập: <b>${data.TenNguoiLap || ""}</b></p>
                                    </div>
                                    <div class="col-md-4">
                                        <p class="fs-big my-1">Chức vụ: <b>${data.TenChucVuNguoiLap || ""}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="dropdown text-end" style="position: absolute; top: 10px; right: 10px;">
                                    <button class="btn btn-sm btn-white dropdown-toggle-hide-arrow" type="button" style="font-size:18px" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="blue fa fa-ellipsis-h" style="color: #696cff"></i>
                                    </button>
                                    <div class="dropdown-menu dropdown-menu-end w-auto">
                                    <!-- Luôn hiển thị: Xem (luôn ở đầu) -->
                                    <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                                    href="javascript:void(0);" onclick="XemChiTietNhatKy('${data.id}'); return false;">
                                        ${icons["xem"]}&ensp; Xem đơn yêu cầu
                                    </a>

                                    <!-- TrangThaiXuLyID == 41 (Đã gửi) -->
                                    ${data.TrangThaiXuLyID == '41' ? `
                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                                        href="javascript:void(0);" onclick="SuaDuLieu('${data.id}'); return false;">
                                            ${icons["sua"]}&ensp; Chỉnh sửa đơn yêu cầu
                                        </a>
                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                                        href="javascript:void(0);" onclick="GuiDonXinCapPhoiBang('${data.id}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                                            ${icons["guiduyet"]}&ensp; Gửi đơn yêu cầu
                                        </a>
                                    ` : ''}

                                    <!-- TrangThaiXuLyID == 40 (Chờ duyệt) -->
                                    ${data.TrangThaiXuLyID == '40' ? `
                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                                        href="javascript:void(0);" onclick="ThuHoiDonXinCapPhoiBang('${data.id}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                                            ${icons["thuhoiBH"]}&ensp; Thu hồi đơn xin cấp phôi bằng
                                        </a>
                                    ` : ''}

                                    <!-- Luôn hiển thị: In -->
                                    <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                                    href="javascript:void(0);" onclick="InPhieuDeNghi('${data.id}')">
                                        ${icons["in"]}&ensp; In đơn yêu cầu
                                    </a>

                                    <!-- Luôn hiển thị: Xóa (luôn ở cuối) -->
                                    <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                                    href="javascript:void(0);" onclick="XoaDuLieu('${data.id}')">
                                        ${icons["xoa"]}&ensp; Xóa đơn yêu cầu
                                    </a>
                                </div>

                                </div>
                            </div>
                        </div>
                        <div class="row">
                        <hr class="nts-hr">
                            <div class="col-md-11">
                                <div class="row mb-2">
                                    <div class="col-md-8">
                                        <p class="fs-big my-1">Đơn vị yêu cầu: <b>${data.TenDonViGui || ""}</b></p>
                                    </div>
                                    <div class="col-md-4">
                                        <p class="fs-big my-1">Học sinh: <b>${data.TenHocSinh || ""}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1"></div>
                            <div class="col-md-11">
                                <div class="row mb-2">
                                    <div class="col-md-4">
                                        <p class="fs-big my-1">Lý do chỉnh sửa: <b>${data.LyDoDieuChinh || ""}</b></p>
                                    </div>
                                    <div class="col-md-4">
                                        <p class="fs-big my-1">Số hiệu: <b>${data.SoHieu || ""}</b></p>
                                    </div>
                                     <div class="col-md-4">
                                        <p class="fs-big my-1">Số vào sổ: <b>${data.SoVaoSo || ""}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1"></div>
                                <div class="col-md-11">
                                    <div class="row mb-12">
                                        <div class="col-md-8">
                                            <p class="fs-big my-1">Đơn vị tiếp nhận: <b>${data.TenDonViNhan || ""}</b></p>
                                        </div>
                                        <div class="col-sm-4" id="txtDinhKemHTML" style=" padding: 4px; ">
                                            <p class="fs-big my-1">
                                                Đính kèm:
                                                <a href="#" data="" onclick="XemDinhKem_us('`+ data.DinhKem + `')">
                                                    <i class="fa fa-paperclip me-1"></i> Xem đính kèm
                                                </a>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            <div class="col-md-1"></div>
                        </div>
                        <div class="row">
                        <hr class="nts-hr">
                        </div>
                        <div class="row">
                            <div class="col-md-11">
                                <div class="row mb-2">
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">${labelNgay}: <b>${txtNgay}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">${labelNguoi}: <b>${tenNguoi}</b></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="fs-big my-1">${labelChucVu}: <b>${chucVu}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1"></div>
                            <div class="col-md-11">
                                <div class="row mb-2">
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">${labelNoiDung}: <b>${noiDung}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1"></div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>`;
}

var table = new Tabulator("#Grid1", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "600",
    HeaderVertAlign: "center",
    headerVisible: false,
    columns: [
        {
            title: "Thông tin",
            field: "ThongTinHoGiaDinh",
            formatter: htmlDuLieu,
            visible: true,
            minWidth: 250,
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});

async function LoadDataTable() {
    table.clearData();
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getAll",
        {
            SearchKey: $("#SearchKey").value(),
            LoaiPhoiID_Loc: $("#LoaiPhoiID_Loc").value(),
            TrangThaiXuLyID_Loc: $("#TrangThaiXuLyID_Loc").value(),
        }
    );
    if (!result.Err) {
        VeChart();
        table.setData(result.result);
    } else {
        table.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

$(document).on("click", "#TimKiem", async function () {
    $("#KhungTimKiem").slideUp(200);
    await LoadDataTable();
    await LoadDataTable2();
    return false;
});

$(document).on("keyup", "#SearchKey", async function (e) {
    if (e.keyCode == "13") {
        await LoadDataTable();
        $("#KhungTimKiem").slideUp(200);
        return false;
    }
});

$(document).on("change", "#CbSapXep", function () {
    LoadDataTable();
});

$(document).on("click", "#btn-layout-1", async function () {
    $("#grid-layout").fadeIn(200);
    $("#list-layout").hide();
    $("#list-layout").removeClass("show");
    $("#grid-layout").addClass("show");
    await LoadDataTable();
});

$(document).on("click", "#btn-layout-2", async function () {
    $("#grid-layout").hide();
    $("#list-layout").fadeIn(200);
    $("#list-layout").addClass("show");
    $("#grid-layout").removeClass("show");
    $("#YeuCauCSNDVBCCID").value("");
    $(".divThaoTacNhanh").hide();
    $("#txtTenTrangThaiXuLy_View").html("Trạng thái xử lý: <b>---</b>");
    $("#txtTenDonViGui_View").html("Đơn vị gửi: <b>---</b>");
    await LoadDataTable2();
});

function actionDropdownFormatter(cell) {
    const data = cell.getData();
    const ID = data.id;
    const button = document.createElement("button");
    button.className = "btn btn-sm btn-white dropdown-toggle-hide-arrow";
    button.innerHTML = `<i class="fa fa-ellipsis-h" style="color: #696cff;"></i>`;
    button.style.boxShadow = "none";

    button.onclick = function (e) {
        e.stopPropagation();
        document.querySelectorAll('.custom-dropdown-menu').forEach(el => el.remove());

        const dropdown = document.createElement("div");
        dropdown.className = "custom-dropdown-menu dropdown-menu dropdown-menu-end show";
        dropdown.style.position = "absolute";
        dropdown.style.zIndex = 9999;
        dropdown.style.minWidth = "220px";

        const TrangThai = data.TrangThaiXuLyID;
        let html = `
            <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                href="javascript:void(0);" onclick="XemChiTietNhatKy('${ID}'); return false;">
                ${icons["xem"]}&ensp; Xem đơn yêu cầu
            </a>
        `;

        if (TrangThai == '41') {
            html += `
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="SuaDuLieu('${ID}'); return false;">
                    ${icons["sua"]}&ensp;Chỉnh sửa đơn yêu cầu
                </a>
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="GuiDonXinCapPhoiBang('${ID}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                    ${icons["guiduyet"]}&ensp; Gửi đơn yêu cầu
                </a>
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="XoaDuLieu('${ID}')">
                    ${icons["xoa"]}&ensp; Xóa đơn yêu cầu
                </a>
            `;
        }

        if (TrangThai == '40') {
            html += `
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="ThuHoiDonXinCapPhoiBang('${ID}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                    ${icons["thuhoiBH"]}&ensp; Thu hồi đơn xin cấp phôi bằng
                </a>
            `;
        }

        html += `
            <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                href="javascript:void(0);" onclick="InPhieuDeNghi('${ID}')">
                ${icons["in"]}&ensp; In đơn yêu cầu
            </a>
        `;

        dropdown.innerHTML = html;

        const rect = button.getBoundingClientRect();
        dropdown.style.left = `${rect.left + window.scrollX}px`;
        dropdown.style.top = `${rect.bottom + window.scrollY}px`;

        document.body.appendChild(dropdown);

        const closeDropdown = (event) => {
            if (!dropdown.contains(event.target) && event.target !== button) {
                dropdown.remove();
                document.removeEventListener('click', closeDropdown);
            }
        };
        document.addEventListener('click', closeDropdown);
    };

    return button;
}


function trangThaiSpanFormatter(cell) {
    const data = cell.getData();

    const mauSac = data.MauSacTrangThaiXuLy || "#6c757d";
    const tenTrangThai = data.TenTrangThaiXuLy || "Không xác định";

    const container = document.createElement("div");
    container.style.display = "flex";
    container.style.justifyContent = "center";

    const span = document.createElement("span");
    span.style.display = "block";
    span.style.marginTop = "10px";
    span.style.fontWeight = "bold";
    span.style.fontSize = "1rem";
    span.style.textAlign = "center";
    span.style.padding = "6px 0";
    span.style.backgroundColor = mauSac;
    span.style.color = "white";
    span.style.borderRadius = "6px";
    span.style.width = "95%";
    span.innerText = tenTrangThai;

    container.appendChild(span);
    return container;
}


var GridMainLuoi = new Tabulator("#GridMainLuoi", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "550",
    HeaderVertAlign: "center",
    columns: [
        {
            title: '<i class="fa fa-ellipsis-h"></i>',
            headerHozAlign: "center",
            hozAlign: "center",
            formatter: actionDropdownFormatter,
            width: 60,
            headerSort: false,
            frozen: true,
            vertAlign: "middle",
            print: false
        },
        {
            title: "Ngày lập",
            field: "txtNgayLap",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Người lập",
            field: "TenNguoiLap",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 150,
        },
        {
            title: "Chức vụ người lập",
            field: "TenChucVuNguoiLap",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 150,
        },
        {
            title: "Đơn vị yêu cầu",
            field: "TenDonViGui",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 300,
        },
        {
            title: "Đơn vị tiếp nhận",
            field: "TenDonViNhan",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 300,
        },
        {
            title: "Học sinh",
            field: "TenHocSinh",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 150,
        },
        {
            title: "Số hiệu",
            field: "SoHieu",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Số và sổ",
            field: "SoVaoSo",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Lý do chỉnh sửa",
            field: "LyDoDieuChinh",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "right",
            vertAlign: "middle",
            width: 300,
        },
        {
            title: "Trạng thái",
            field: "TenTrangThaiXuLy",
            formatter: "textarea",
            hozAlign: "center",
            headerHozAlign: "center",
            hozAlign: "right",
            vertAlign: "middle",
            width: 120,
        }


    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});

async function LoadDataTable2() {

    GridMainLuoi.clearData();
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getAll",
        {
            SearchKey: $("#SearchKey").value(),
            LoaiPhoiID_Loc: $("#LoaiPhoiID_Loc").value(),
            TrangThaiXuLyID_Loc: $("#TrangThaiXuLyID_Loc").value(),
            CbSapXep: $("#CbSapXep").value(),
        }
    );
    if (!result.Err) {
        GridMainLuoi.setData(result.result);
    } else {
        GridMainLuoi.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}


//#region Lưới 2
var table2 = new Tabulator("#Grid2", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "500",
    HeaderVertAlign: "center",
    headerVisible: false,
    selectable: 1,
    //data: dataDoiTuong,
    columns: [
        {
            title: "Thông tin hộ gia đình",
            field: "ThongTinNguoiThamGia",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 150,
            headerSort: false,
        },
    ],
    rowFormatter: function (row) {
        var element = row.getElement(),
            data = row.getData(),
            width = element.offsetWidth,
            rowTable,
            cellContents;
        while (element.firstChild) element.removeChild(element.firstChild);
        rowTable = document.createElement("table");
        rowTable.style.width = width - 18 + "px";
        rowTabletr = document.createElement("tr");
        cellContents =
            "<td><img style='width: 50px;height: 50px;max-width: unset !important;' src='" +
            window.Laravel.local.linkAnhDonXin +
            "'></td>";
        cellContents +=
            "<td><div style='text-align: left'><strong>" +
            data.TenTrangThaiXuLy + "--" + data.TenDonViGui
        "</strong> </div><div style='text-align: left !important;font-size: 12px!important;'> CCCD/CMT: <b>" +
            (data.TenDonViGui || ChuaCoThongTin) + " - " + (data.txtNgayLap || ChuaCoThongTin) +
            "</b></div></td>";
        rowTabletr.innerHTML = cellContents;
        rowTable.appendChild(rowTabletr);
        element.append(rowTable);
    },
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});
let currentSelectedRowID = null;
GridMainLuoi.on("rowClick", async function (e, row) {
    const selectedID = row.getData().id;

    if (selectedID !== currentSelectedRowID) {
        currentSelectedRowID = selectedID;
        $("#YeuCauCSNDVBCCID").val(selectedID);
        await LoadTabThongTinDonXin();
    } else {
        currentSelectedRowID = null;
        $("#YeuCauCSNDVBCCID").val("");
        await LoadTabThongTinDonXin();
    }
});
async function LoadDataTable2() {

    GridMainLuoi.clearData();
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getAll",
        {
            SearchKey: $("#SearchKey").value(),
            LoaiPhoiID_Loc: $("#LoaiPhoiID_Loc").value(),
            TrangThaiXuLyID_Loc: $("#TrangThaiXuLyID_Loc").value(),
            CbSapXep: $("#CbSapXep").value(),
        }
    );
    if (!result.Err) {
        GridMainLuoi.setData(result.result);
    } else {
        GridMainLuoi.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}
//#endregion
async function LoadTabThongTinDonXin() {
    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/loaddulieusua",
        { id: $("#YeuCauCSNDVBCCID").val() }
    );
    if (!result.Err && result.Result != null) {
        let data = result.Result;
        if (data != undefined) {
            $(".divThaoTacNhanh").show();
            $("#dxcpb_NgayLap").text(data.txtNgayLap || ChuaCoThongTin);
            $("#dxcpb_NguoiLap").text(data.TenNguoiLap || ChuaCoThongTin);
            $("#dxcpb_ChucVuNguoiLap").text(data.TenChucVuNguoiLap || ChuaCoThongTin);
            $("#dxcpb_DonViGui").text(data.TenDonViGui || ChuaCoThongTin);
            $("#dxcpb_LoaiPhoi").text(data.TenLoaiPhoi || ChuaCoThongTin);
            $("#dxcpb_SoLuong").text(data.SoLuong || ChuaCoThongTin);
            $("#dxcpb_DonViNhan").text((data.TenDonViNhan || ChuaCoThongTin) + (data.MaDonViNhan ? ` (${data.MaDonViNhan})` : ""));
            $("#dxcpb_LyDoXinCap").text(data.LyDoXinCap || ChuaCoThongTin);
            $("#txtTenTrangThaiXuLy_View").text(data.TenTrangThaiXuLy || ChuaCoThongTin);
            $("#txtTenDonViGui_View").text(data.TenDonViGui || ChuaCoThongTin);



            if (data.DinhKem) {
                $("#dxcpb_DinhKem").html(`<a href="${data.LinkDinhKem || '#'}" target="_blank">${data.TenTepDinhKem || 'Tệp đính kèm'}</a>`);
            } else {
                $("#dxcpb_DinhKem").html(ChuaCoThongTin);
            }
            $("#dxcpb_GhiChu").text(data.GhiChu || ChuaCoThongTin);

            let labelNgay = "Ngày xử lý", labelNguoi = "Người xử lý", labelChucVu = "Chức vụ", labelNoiDung = "Nội dung";
            if (data.TrangThaiXuLyID == 40) {
                labelNgay = "Ngày gửi";
                labelNguoi = "Người gửi";
                labelChucVu = "Chức vụ người gửi";
                labelNoiDung = "Nội dung gửi";
            } else if (data.TrangThaiXuLyID == 41) {
                labelNgay = "Ngày đề nghị";
                labelNguoi = "Người đề nghị";
                labelChucVu = "Chức vụ người đề nghị";
                labelNoiDung = "Nội dung đề nghị";
            } else if (data.TrangThaiXuLyID == 42) {
                labelNgay = "Ngày từ chối";
                labelNguoi = "Người từ chối";
                labelChucVu = "Chức vụ người từ chối";
                labelNoiDung = "Lý do từ chối";
            }
            $("#dxcpb_LabelNgay").text(labelNgay);
            $("#dxcpb_LabelNguoi").text(labelNguoi);
            $("#dxcpb_LabelChucVu").text(labelChucVu);
            $("#dxcpb_LabelNoiDung").text(labelNoiDung);
            $("#dxcpb_NgayXuLy").text(data.txtNgayXuLy || ChuaCoThongTin);
            $("#dxcpb_NguoiXuLy").text(data.TenNguoiXuLy || ChuaCoThongTin);
            $("#dxcpb_ChucVuNguoiXuLy").text(data.TenChucVuNguoiXuLy || ChuaCoThongTin);
            $("#dxcpb_NoiDungXuLy").text(data.NoiDungXuLy || ChuaCoThongTin);
            // Cập nhật action buttons
            const id = data.id;
            // Sửa
            $("#btnSuaChiTiet").off("click").on("click", function () { SuaDuLieu(id); });
            // Gửi đơn
            if (data.TrangThaiXuLyID != 40) {
                $("#btnGuiDonChiTiet").show().off("click").on("click", function () { GuiDonXinCapPhoiBang(id); });
            } else {
                $("#btnGuiDonChiTiet").hide();
            }
            // Thu hồi
            if (data.TrangThaiXuLyID != 41) {
                $("#btnThuHoiChiTiet").show().off("click").on("click", function () { ThuHoiDonXinCapPhoiBang(id); });
            } else {
                $("#btnThuHoiChiTiet").hide();
            }
            // Nhật ký thao tác
            $("#btnNhatKyChiTiet").off("click").on("click", function () { XemChiTietNhatKy(id); });
            // Xóa
            $("#btnXoaChiTiet").off("click").on("click", function () { XoaDuLieu(id); });
        }
    } else {
        $(".divThaoTacNhanh").hide();
        // Clear các trường mới
        $("#dxcpb_NgayLap, #dxcpb_NguoiLap, #dxcpb_ChucVuNguoiLap, #dxcpb_DonViGui, #dxcpb_LoaiPhoi, #dxcpb_SoLuong, #dxcpb_DonViNhan, #dxcpb_LyDoXinCap, #dxcpb_DinhKem, #dxcpb_GhiChu, #dxcpb_LabelNgay, #dxcpb_LabelNguoi, #dxcpb_LabelChucVu, #dxcpb_LabelNoiDung, #dxcpb_NgayXuLy, #dxcpb_NguoiXuLy, #dxcpb_ChucVuNguoiXuLy, #dxcpb_NoiDungXuLy").text(ChuaCoThongTin);
        $("#dxcpb_DinhKem").html(ChuaCoThongTin);
    }
}

$("#btnGuiDon").on("click", async function () {

    const validate = new NTSValidate("#mdGuiDonXinCapPhoiBang");
    if (!validate.trim().check()) return false;

    const payload = {
        YeuCauCSNDVBCCID: $("#YeuCauCSNDVBCCID").val(),
        NguoiXuLyID: $("#NguoiXuLyID").val(),
        ChucVuNguoiXuLyID: $("#ChucVuNguoiXuLyID").val(),
        NoiDungXuLy: $("#NoiDungXuLy").val(),
        NgayXuLy: $("#NgayXuLy").val(),
        DonViTiepNhanXuLyID: tempTrangThai === "41" ? null : $("#DonViTiepNhanXuLyID").val(),
        TrangThaiXuLyID: tempTrangThai
    };

    var result = await NTS.getAjaxAPIAsync(
        "post",
        window.location.pathname + "/GuiDon",
        payload
    );
    if (!result.Err) {
        LoadDataTable();
        NTS.thanhcong(result.Msg);
        $("#mdGuiDonXinCapPhoiBang").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});

async function VeChart() {
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.local.getThongKe,
        {
            SearchKey: $("#SearchKey").value(),
            LoaiPhoiID_Loc: $("#LoaiPhoiID_Loc").value(),
            TrangThaiXuLyID_Loc: $("#TrangThaiXuLyID_Loc").value(),
        }
    );

    try {
        $("#txtTong").text(result.Result.TS);

        const trangThaiData = result.Result.ThongKeTrangThai || [];
        const container = $("#trangThaiContainer").empty();

        const iconClassMap = {
            "Đã phê duyệt": "fa-check-square",
            "Đã duyệt": "fa-check-square",
            "Chờ duyệt": "fa-clock-o",
            "Chờ gửi": "fa-clock-o",
            "Bị từ chối": "fa-ban",
        };

        const chartLabels = [];
        const chartSeries = [];
        const chartColors = [];

        trangThaiData.forEach(item => {
            const icon = iconClassMap[item.TenTrangThai] || "fa-info-circle";
            container.append(`
                <div style="color: ${item.MauSac}; margin-bottom: 7px;">
                    <i class="fa ${icon}" aria-hidden="true"></i>
                    ${item.TenTrangThai}:
                    <b><u><b id="txtTrangThai_${item.TrangThaiXuLyID}">${item.SoLuong}</b></u></b>
                </div>
            `);
            chartLabels.push(item.TenTrangThai);
            chartSeries.push(item.SoLuong);
            chartColors.push(item.MauSac || "#999999");
        });

        const $container = $("#phoiContainer").empty();
        const ThongKeLoaiPhoi = result.Result.ThongKeLoaiPhoi || [];

        ThongKeLoaiPhoi.forEach((item, index) => {
            const isEven = index % 2 === 0;
            const color = isEven ? "#7AA802" : "#F78B2D";
            const iconSvg = isEven ? `<svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17"
                                        fill="none">
                                        <path
                                            d="M2.73756 2.05261C1.97809 2.05261 1.36914 2.66156 1.36914 3.42103V10.2631C1.36914 10.6261 1.51331 10.9741 1.76994 11.2308C2.02657 11.4874 2.37463 11.6316 2.73756 11.6316H8.21125V15.0526L10.2639 13L12.3165 15.0526V11.6316H13.6849C14.0479 11.6316 14.3959 11.4874 14.6525 11.2308C14.9092 10.9741 15.0534 10.6261 15.0534 10.2631V3.42103C15.0534 3.05811 14.9092 2.71004 14.6525 2.45341C14.3959 2.19678 14.0479 2.05261 13.6849 2.05261H2.73756ZM8.21125 3.42103L10.2639 4.78945L12.3165 3.42103V5.81577L14.3691 6.84209L12.3165 7.8684V10.2631L10.2639 8.89472L8.21125 10.2631V7.8684L6.15861 6.84209L8.21125 5.81577V3.42103ZM2.73756 3.42103H6.15861V4.78945H2.73756V3.42103ZM2.73756 6.15788H4.79019V7.5263H2.73756V6.15788ZM2.73756 8.89472H6.15861V10.2631H2.73756V8.89472Z"
                                            fill="#7AA802" />
                                     </svg>` : `<svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17"
                                        fill="none">
                                        <path
                                            d="M8.21083 13C9.34447 13 10.2635 12.081 10.2635 10.9473C10.2635 9.81371 9.34447 8.89471 8.21083 8.89471C7.0772 8.89471 6.1582 9.81371 6.1582 10.9473C6.1582 12.081 7.0772 13 8.21083 13Z"
                                            stroke="#F78B2D" stroke-width="1.02632" />
                                        <path
                                            d="M6.23612 10.92L5.52454 11.6316L4.51328 12.6011C4.2916 12.8139 4.18076 12.9199 4.14244 13.0102C4.09936 13.1063 4.09372 13.215 4.12663 13.315C4.15954 13.415 4.22863 13.4991 4.32033 13.5508C4.40381 13.598 4.55502 13.6124 4.85607 13.6425C5.02576 13.6589 5.11128 13.6671 5.18244 13.6924C5.25976 13.7193 5.33037 13.7625 5.38941 13.8192C5.44846 13.8759 5.49456 13.9447 5.52454 14.0208C5.55123 14.0892 5.56012 14.1707 5.57723 14.3342C5.60802 14.6229 5.62376 14.7673 5.67302 14.8474C5.78523 15.03 6.02265 15.1019 6.23681 15.0184C6.33055 14.9808 6.44139 14.8747 6.66307 14.6626L8.21144 13.1779L9.75981 14.6626C9.98149 14.8747 10.0923 14.9808 10.1861 15.0184C10.4002 15.1019 10.6377 15.03 10.7499 14.8474C10.7991 14.7673 10.8149 14.6229 10.8457 14.3342C10.8628 14.1707 10.8717 14.0892 10.8983 14.0208C10.9283 13.9447 10.9744 13.8759 11.0335 13.8192C11.0925 13.7625 11.1631 13.7193 11.2404 13.6924C11.3123 13.6671 11.3971 13.6589 11.5668 13.6425C11.8679 13.613 12.0191 13.598 12.1025 13.5508C12.1943 13.4991 12.2633 13.415 12.2963 13.315C12.3292 13.215 12.3235 13.1063 12.2804 13.0102C12.2421 12.9199 12.1313 12.8139 11.9096 12.6011L10.8977 11.6316L10.2641 10.9973"
                                            stroke="#F78B2D" stroke-width="1.02632" />
                                        <path
                                            d="M11.8512 12.313C13.2005 12.2987 13.9531 12.2138 14.4519 11.7144C15.0534 11.1136 15.0534 10.1455 15.0534 8.21051V5.47367C15.0534 3.53872 15.0534 2.57057 14.4519 1.96983C13.8512 1.36841 12.883 1.36841 10.9481 1.36841H5.4744C3.53946 1.36841 2.5713 1.36841 1.97056 1.96983C1.36914 2.57057 1.36914 3.53872 1.36914 5.47367V8.21051C1.36914 10.1455 1.36914 11.1136 1.97056 11.7144C2.49604 12.2405 3.30204 12.3062 4.79019 12.3144"
                                            stroke="#F78B2D" stroke-width="1.02632" />
                                        <path d="M6.15944 4.10522H10.2647M4.79102 6.49996H11.6331" stroke="#F78B2D"
                                            stroke-width="1.02632" stroke-linecap="round" />
                                    </svg>`;

            $container.append(`
                <div style="color: ${color}; margin-bottom: 7px;">
                    ${iconSvg} <span>${item.TenLoaiPhoiVanBangChungChi}</span>: 
                    <b><u><b>${item.SoLuong}</b></u></b>
                </div>
            `);
        });

        document.querySelector("#XepLoaiChart").innerHTML = "";

        mixedXepLoaiChart = new ApexCharts(document.querySelector("#XepLoaiChart"), {
            labels: chartLabels,
            colors: chartColors,
            series: chartSeries,
            chart: {
                type: "donut",
                height: 150
            },
            dataLabels: {
                enabled: false,
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 110,
                    },
                    legend: {
                        position: "bottom",
                    },
                },
            }],
            dataLabels: {
                enabled: true,
                formatter: function (val) {
                    return Math.round(val) + "%";
                },
                style: {
                    fontSize: '12px',
                    fontWeight: 'bold',
                    colors: ['#ffffff'],
                    fontFamily: 'Arial'
                }
            },
            plotOptions: {
                pie: {
                    donut: {
                        size: '55%',
                        labels: {
                            show: false
                        }
                    }
                }
            }
        });

        mixedXepLoaiChart.render();

    } catch (err) {
        console.error("Lỗi khi vẽ biểu đồ:", err);
    }
}