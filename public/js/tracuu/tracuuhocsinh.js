var ChuaCoThongTin = "";
///////// PHÍM TẮT /////////
var hotKey = 0;
var currentAvatarPath = "";
$(document).on("keydown", function (e) {
    switch (e.keyCode) {
        case 114:
            if (hotKey == 0) $(".nav-search-input").focus();
            e.preventDefault();
            break;
        case 115:
            if (hotKey == 1) $("#mdXemChiTietHocSinh_us").modal("hide");
            e.preventDefault();
            break;
    }
});
$(document).on("shown.bs.modal", "#mdXemChiTietHocSinh_us", function () {
    hotKey = 1;
});
$(document).on("hidden.bs.modal", "#mdXemChiTietHocSinh_us", function () {
    hotKey = 0;
});
$(function () {
    $('#TieuDeTrang').hide();
     NTS.loadDataCombo({
        name: "#DonViID_TimKiem",
        type: "POST",
        ajaxUrl: window.Laravel.layouts.GetDSDonViAll,
        ajaxParam: {},
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "--Tất cả--",
        showTatCa: !0,
    });
    NTS.loadDataCombo({
        name: '#DiaBanHCID_Tinh_TimKiem',
        ajaxUrl: window.Laravel.tracuuhocsinh.GetDSDiaBanHCTinh,
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: '--Tất cả--',
        showTatCa: !0
    });

     NTS.loadDataCombo({
        name: '#DiaBanHCID_Xa_TimKiem',
        ajaxUrl: window.Laravel.tracuuhocsinh.GetDSDiaBanHC_ByIDCha,
        ajaxParam: { DiaBanHCID_Cha: '' },
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: '--Tất cả--',
        showTatCa: !0
    });

      NTS.loadDataCombo({
        name: "#TrangThaiCBID_TimKiem",
        type: "POST",
        ajaxUrl: window.Laravel.layouts.GetDSTrangThaiAll,
        ajaxParam: {},
        columns: 2,
        indexValue: 0,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "--Tất cả--",
        showTatCa: !0,
    });

});

$(document).on('change', '#DiaBanHCID_Tinh_TimKiem',async function () {
    await NTS.loadDataComboAsync({
        name: '#DiaBanHCID_Xa_TimKiem',
        ajaxUrl: window.Laravel.tracuuhocsinh.GetDSDiaBanHC_ByIDCha,
        ajaxParam: { DiaBanHCID_Cha: $('#DiaBanHCID_Tinh_TimKiem').value() },
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: '--Tất cả--',
        showTatCa: !0
    });

});

async function LoadDataTable() {
    GridMainDS.clearData();
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getall",
           {
            timKiemG:           $("#timKiemG").val(),                 // giống tên PHP
            DiaBanHCID_Tinh_TimKiem: $("#DiaBanHCID_Tinh_TimKiem").val(),
            DiaBanHCID_Xa_TimKiem:   $("#DiaBanHCID_Xa_TimKiem").val(),
            TrangThaiCBID_TimKiem:     $("#TrangThaiCBID_TimKiem").val(),
            DonViID_TimKiem:         $("#DonViID_TimKiem").val(),
            CbSapXep:               $("#CbSapXep").val()
        }
    );
    if (!result.Err) {
         var data = result.result;
        GridMainDS.setData(data);
        $('#lblSoLuongKetQua').text(data.length);

    } else {
        GridMainDS.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

async function LoadDataTable_Tabu() {
    GridMainLuoi.clearData();

    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getall",
        {
            timKiemG:           $("#timKiemG").val(),                 // giống tên PHP
            DiaBanHCID_Tinh_TimKiem: $("#DiaBanHCID_Tinh_TimKiem").val(),
            DiaBanHCID_Xa_TimKiem:   $("#DiaBanHCID_Xa_TimKiem").val(),
            TrangThaiCBID_TimKiem:     $("#TrangThaiCBID_TimKiem").val(),
            DonViID_TimKiem:         $("#DonViID_TimKiem").val(),
            CbSapXep:               $("#CbSapXep").val()
        }
    );

    if (!result.Err) {
        const data = result.result;
        GridMainLuoi.setData(data);
        $('#lblSoLuongKetQua').text(data.length);
    } else {
        GridMainLuoi.setData([]);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}


function htmlDuLieu(cell, formatterParams, onRendered) {
    let anhDaiDien = "";
    let url_anhDaiDien = "";

    const data = cell.getData();
    const baseAsset = window.Laravel.tracuuhocsinh.fileIconUrl.replace(/\/$/, ""); // bỏ dấu "/" cuối nếu có
    let path = data.AnhDaiDien?.replaceAll("~", "").replaceAll("*", "") || "";
    path = path.replace(/^\//, ""); // bỏ dấu "/" đầu nếu có

    if (!path) {
        url_anhDaiDien = window.Laravel.tracuuhocsinh.imgUser;
    } else {
        url_anhDaiDien = `${baseAsset}/${path}`; // DÙNG path đã xử lý
    }

    anhDaiDien = `<img src="${url_anhDaiDien}" alt="${data.Hovaten}" class="img-thumbnail rounded lazy">`;

    return `<div class="list-item col-md-12" style="padding: 0px;">
                        <div class="card card-luoi shadow-sm mb-2 ">
                            <div id="card_${cell.getData().id
        }" class="card-body profile-user-box">
                                <div class="row">
                                    <div class="col-12 col-xs-6 col-sm-2 center" style="margin: auto;">
                                        <div class="profile-picture">
                                            ${anhDaiDien}
                                        </div>
                                    </div>
                                    <div class="col-md-10">
                                        <div class="row">
                                            <div class="col-sm-10">
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <p class="fs-big my-1">Mã học sinh: <b>${cell.getData()
            .MaDoiTuong ||
        ChuaCoThongTin
        }</b></p>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <p class="fs-big my-1">Tên học sinh: <b>${cell.getData()
            .Hovaten ||
        ChuaCoThongTin
        }</b></p>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <p class="fs-big my-1">Ngày sinh: <b>${cell.getData()
            .txtNgaysinh ||
        ChuaCoThongTin
        }</b></p>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <p class="fs-big my-1">Giới tính: <b>${cell.getData()
            .txtGioitinh ||
        ChuaCoThongTin
        }</b></p>
                                                    </div>

                                                    <div class="col-md-4">
                                                        <p class="fs-big my-1">Dân tộc: <b>${cell.getData()
            .TenDanToc ||
        ChuaCoThongTin
        }</b></p>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <p class="fs-big my-1">CMND/CCCD: <b>${cell.getData()
            .CCCD ||
        ChuaCoThongTin
        }</b></p>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <p class="fs-big my-1">Cấp ngày:<b> ${cell.getData()
            .txtNgayCap ||
        ChuaCoThongTin
        }</b></p>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <p class="fs-big my-1">Nơi cấp: <b>${cell.getData()
            .TenNoiCap ||
        ChuaCoThongTin
        }</b></p>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <p class="fs-big my-1">Hộ khẩu thường trú: <b>${cell.getData()
            .DiaChi ||
        ChuaCoThongTin
        }</b></p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-2">
                                                <div class="dropdown text-end" style="position: absolute; top: 10px; right: 10px">
                                                    <button class="btn btn-sm btn-white dropdown-toggle-hide-arrow" type="button" style="font-size:18px" id="growthReportId" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                        <i class="blue fa fa-ellipsis-h" style="color: #696cff"></i>
                                                    </button>
                                                    <div class="dropdown-menu dropdown-menu-end w-auto" >
                                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="XemChiTietHocSinh_us('${cell.getData().id
        }'); return false;">${icons["xem"]}&ensp; Xem học sinh</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-top:4px; margin-bottom: -20px;">
                                    <div class="col-md-2" style="text-align: center;">
                                        <span id="txtTrangThaiTN" class="alert d-inline-block span-trangthai" style="background-color:${cell.getData().MauSac_TotNghiep};">
                                        ${cell.getData().TenTrangThai_TotNghiep}
                                        </span>
                                    </div>
                                    <div class="col-md-10">
                                        <hr class="nts-hr"/>
                                        <div class="row">
                                            <div class="col-md-10">
                                                <div class="row">
                                                    <div class="col-md-8">
                                                            <p class="fs-big my-1">Thuộc trường: <b>${cell.getData()
            .TenDonVi ||
        ChuaCoThongTin
        }</b></p>
                                                    </div>
                                                    <div class="col-md-4">
                                                            <p class="fs-big my-1">Lớp: <b>${cell.getData().TenLopHoc ||
        ChuaCoThongTin
        }</b></p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-2 text-end mt-1">
                                                <span id="txtTrangThaiCapBang" class="alert d-inline-block span-trangthai" style="background-color:${cell.getData().MauSac_CapBang};">
                                                   <i class="fa fa-check-circle-o" aria-hidden="true"></i> ${cell.getData().TenTrangThai_CapBang}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>`;
}
//Lưới danh sách tra cứu

var GridMainDS = new Tabulator("#GridMainDS", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "600",
    HeaderVertAlign: "center",
    headerVisible: false,
    columns: [
        { title: "Thông tin", field: "ThongTinHocSinh",formatter: htmlDuLieu, visible: true,minWidth: 250,},
        { title: "MaDoiTuong", field: "MaDoiTuong", visible: false, width: 100, headerSort: true, },
        { title: "Hovaten", field: "Hovaten", visible: false, width: 250, headerSort: true, },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});
var TieuDeTrang = $('#TieuDeTrang').html().trim();

$(document).on('click', '.btnXemGridMainLuoi', function () {
    if (!QuyenThem()) {
        return false;
    }
    var ID = $(this).attr('data');
    XemChiTietHocSinh_us(ID);
});

function QuayLaiDivThongTinChung() {
    $('#DivChiTiet_us').hide();
    $('#Div1, #DivMain').show();
    AnHienMenuV2(false);
    $('#TieuDeTrang').html(TieuDeTrang);
}

$(document).on('click', '#btnAnHienTQ', function () {
    var divThongKe = document.getElementById("DivThongKe");
    var textAnHien = document.getElementById("textAnHien");
    var iconAnHien = document.getElementById("iconAnHien");
    if (iconAnHien.className == "fa fa-eye-slash") {
        //divThongKe.style.display = "none";
        $('#DivThongKe').slideUp(200);
        iconAnHien.className = "fa fa-eye";
        textAnHien.textContent = " Hiện hướng dẫn tìm kiếm";
    } else {
        //divThongKe.style.display = "unset";
        $('#DivThongKe').slideDown(200);
        iconAnHien.className = "fa fa-eye-slash";
        textAnHien.textContent = " Ẩn hướng dẫn tìm kiếm";
    }

});
var btnThaoTac = function (cell) {
    return `<div class="show-or-hide col-md-12" style="padding-right: 0px; padding-left: 0px; " >
            <a class='text-success btnXemGridMainLuoi nts-btn-xem' style='margin:2px' title="Xem thông tin học sinh" data='${cell.getData().id}' data2='${cell.getData().Hovaten}' data3='${cell.getData().TenLopHoc}'>${icons["xem"]}</a>
            </div>`;
}

var GridMainLuoi = new Tabulator("#GridMainLuoi", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "550",
    HeaderVertAlign: "center",
    locale: true,
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
    paginationCounter: "rows",
    columns: [
        { title: '<i class="fa fa-ellipsis-h"></i>',headerHozAlign: "center",hozAlign: "center", formatter: btnThaoTac, width: 60,headerSort: false,frozen: true, vertAlign: "middle", print: false,},
        { title: "Mã học sinh", headerHozAlign: "center", field: "MaDoiTuong", formatter: 'textarea', hozAlign: "left", visible: true, width: 150, vertAlign: "middle", headerSort: true, },
        { title: "Tên học sinh", headerHozAlign: "center", field: "Hovaten", formatter: 'textarea', hozAlign: "left", visible: true, width: 200, vertAlign: "middle", headerSort: true, },
        { title: "Ngày sinh", headerHozAlign: "center", field: "txtNgaysinh", formatter: 'textarea', hozAlign: "center", visible: true, width: 120, vertAlign: "middle", headerSort: true, },
        { title: "Giới tính", headerHozAlign: "center", field: "txtGioitinh", formatter: 'textarea', hozAlign: "left", visible: true, width: 150, vertAlign: "middle", headerSort: true, },
        { title: "Dân tộc", headerHozAlign: "center", field: "TenDanToc", formatter: 'textarea', hozAlign: "left", visible: true, width: 250, vertAlign: "middle", headerSort: true, },
        { title: "CMND/CCCD", headerHozAlign: "center", field: "CCCD", formatter: 'textarea', hozAlign: "left", visible: true, width: 250, vertAlign: "middle", headerSort: true, },
        { title: "Cấp ngày", headerHozAlign: "center", field: "txtNgayCap", formatter: 'textarea', hozAlign: "left", visible: true, width: 250, vertAlign: "middle", headerSort: true, },
        { title: "Nơi cấp", headerHozAlign: "center", field: "TenNoiCap", formatter: 'textarea', hozAlign: "left", visible: true, width: 250, vertAlign: "middle", headerSort: true, },
        { title: "Hộ khẩu thường trú", headerHozAlign: "center", field: "DiaChi", formatter: 'textarea', hozAlign: "left", visible: true, width: 200, vertAlign: "middle", headerSort: true, },
        { title: "Email", headerHozAlign: "center", field: "Email", formatter: 'textarea', hozAlign: "left", visible: true, width: 250, vertAlign: "middle", headerSort: true, },
        { title: "Số điện thoại", headerHozAlign: "center", field: "SDT", formatter: 'textarea', hozAlign: "left", visible: true, width: 250, vertAlign: "middle", headerSort: true, },
        { title: "Học sinh trường", headerHozAlign: "center", field: "ten_Truong", formatter: 'textarea', hozAlign: "left", visible: true, width: 300, vertAlign: "middle", headerSort: true, },
        { title: "Học sinh lớp", headerHozAlign: "center", field: "TenLopHoc", formatter: 'textarea', hozAlign: "left", visible: true, width: 200, vertAlign: "middle", headerSort: true, },
        { title: "Ghi chú", headerHozAlign: "center", field: "GhiChu", formatter: 'textarea', hozAlign: "left", visible: true, minWidth: 350, vertAlign: "middle", headerSort: true, },
        // { title: "tracuuhocsinhID", field: "id", visible: false },
    ],
});



//Tìm kiếm
$(document).on('click', '#TimKiemNangCao', function () {
    if ($('#KhungTimKiem').css('display') == "block") {
        $('#KhungTimKiem').slideUp(200);
    } else {
        $('#KhungTimKiem').slideDown(200);
        /*LoadTimKiem();*/
    }
    return false;
});
$(document).on('click', '#TimKiem',async function () {
    await LoadDataTable();
    $('#KhungTimKiem').slideUp(200);
    return false;
});
$(document).on('click', '#DongTimKiem', function () {
    $('#KhungTimKiem').slideUp(200);
    return false;
});
function SapXepLuoi() {
    var kiemTraSapXep = $('#BtnSapXepTangGiam i').attr('class').search('fa-sort-alpha-asc');
    if (kiemTraSapXep != -1) { // tìm thấy
        $('#BtnSapXepTangGiam').html('<i class="fa fa-sort-alpha-desc" aria-hidden="true"></i>');
    }
    else {
        $('#BtnSapXepTangGiam').html('<i class="fa fa-sort-alpha-asc" aria-hidden="true"></i>');
    }
    if ($('#btn-layout-1').value() == true) { // Lưới thường
        GridMainLuoi.clearSort();
        GridMainLuoi.setSort([
            { column: $('#CbSapXep').value(), dir: (kiemTraSapXep != -1 ? "desc" : "asc") }, //sort by this first
        ]);
    } else { // Lưới danh sách
        GridMainDS.clearSort();
        GridMainDS.setSort([
            { column: $('#CbSapXep').value(), dir: (kiemTraSapXep != -1 ? "desc" : "asc") }, //sort by this first
        ]);
    }
}

$(document).on('change', '#CbSapXep', function () {
    SapXepLuoi();
});

$(document).on('click', '#BtnSapXepTangGiam', function () {
    SapXepLuoi();
});



$(document).on('click', '#btn-layout-1',async function () {
    var DivLuoi = document.getElementById("DivLuoi");
    var DivDanhSach = document.getElementById("DivDanhSach");
    DivLuoi.style.display = "unset";
    DivDanhSach.style.display = "none";
    await LoadDataTable_Tabu();
    GridMainLuoi.redraw(!0);
});
$(document).on('click', '#btn-layout-2',async function () {
    var DivLuoi = document.getElementById("DivLuoi");
    var DivDanhSach = document.getElementById("DivDanhSach");
    DivLuoi.style.display = "none";
    DivDanhSach.style.display = "unset";
    await LoadDataTable();
    GridMainDS.redraw(!0);
});
$(document).on('keyup', '#timKiemG',async function (e) {
    var data = $(this).val();
    if (e.keyCode == '13') {
        await LoadDataTable();
        $('#KhungTimKiem').slideUp(200);
        return false;
    }
    return false;
});

$(document).on('click', '#btnTimKiem',async function (e) {
    await LoadDataTable();
    $('#KhungTimKiem').slideUp(200);
    return false;
});

$(document).on("click", "#btnXem2", function () {
    XemChiTietHocSinh_us($("#tracuuhocsinhID").value());
});
