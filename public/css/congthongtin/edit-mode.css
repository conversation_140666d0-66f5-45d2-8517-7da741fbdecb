/**
 * Edit Mode Styles for Cổng Thông Tin
 * Styles for drag & drop functionality and edit interface
 */

/* Edit Mode Body */
body.edit-mode {
    position: relative;
}

/* Logo component in edit mode */
body.edit-mode .logo.editable-component {
    border: 2px solid #dc3545 !important;
    border-radius: 6px;
    margin: 5px;
    padding: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: transparent !important;
    pointer-events: auto !important;
}

body.edit-mode .logo.editable-component:hover {
    background-color: rgba(220, 53, 69, 0.1) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.2);
    border-color: #c82333 !important;
}

/* Ensure logo component children don't block clicks */
body.edit-mode .logo.editable-component * {
    pointer-events: none !important;
}

body.edit-mode .logo.editable-component {
    pointer-events: auto !important;
}

/* Editable Sections - Only show in edit mode */
body.edit-mode .editable-section {
    position: relative;
    border: 2px dashed #dc3545 !important;
    border-radius: 8px;
    margin: 10px 0;
    padding: 10px;
    background-color: transparent !important;
    box-shadow: inset 0 0 0 2px rgba(220, 53, 69, 0.1);
}

.editable-section::before {
    display: none;
}

/* Special handling for section-1 to prevent overlay issues */
body.edit-mode .section-1.editable-section {
    border: 2px dashed rgba(220, 53, 69, 0.6) !important;
    box-shadow: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

body.edit-mode .section-1.editable-section::before {
    display: none;
}

/* Ensure section-1 components are not affected - only in edit mode */
body.edit-mode .section-1 .editable-component {
    background: transparent !important;
    margin: 2px !important;
    padding: 5px !important;
    border: 2px solid rgba(220, 53, 69, 0.7) !important;
}

body.edit-mode .section-1 .editable-component:hover {
    background: rgba(220, 53, 69, 0.02) !important;
    border-color: #c82333 !important;
}

/* Ensure hero-image stays with hero-right during drag */
.section-1 .hero-right.dragging .hero-image {
    position: relative !important;
    z-index: inherit !important;
    margin-bottom: 0 !important;
}

/* Ensure all child elements stay with parent during drag */
.editable-component.dragging * {
    position: relative !important;
    z-index: inherit !important;
}

/* Prevent child elements from being draggable separately - only in edit mode */
body.edit-mode .editable-component * {
    pointer-events: none;
}

body.edit-mode .editable-component {
    pointer-events: auto;
}

/* Preserve original styling for hero elements */
.section-1 .hero-left,
.section-1 .hero-right {
    /* Reset any conflicting styles */
}

.section-1 .play-button,
.section-1 .play-icon {
    /* Ensure search button is not affected */
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 50% !important;
    animation: pulse 2s infinite !important;
}

.section-1 .hero-title,
.section-1 .hero-description {
    /* Preserve text styling */
    background: transparent !important;
}

/* Editable Components - Only show in edit mode */
body.edit-mode .editable-component {
    position: relative;
    border: 2px solid #dc3545 !important;
    border-radius: 6px;
    margin: 5px;
    padding: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: transparent !important;
}

/* Exception: Keep card-2 background color but allow flexible dimensions */
body.edit-mode .editable-component.card-2 {
    background-color: rgb(49, 0, 174) !important;
    color: white !important;
    width: 100% !important; /* Take full width of grid cell */
    height: 100% !important; /* Take full height of grid cell */
    min-height: 500px !important; /* Match other cards */
    padding: 40px 30px !important;
}

body.edit-mode .editable-component:hover {
    border-color: #c82333 !important;
    background-color: rgba(220, 53, 69, 0.03) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.2);
}

/* Exception: Keep card-2 hover effects - only in edit mode */
body.edit-mode .editable-component.card-2:hover {
    background-color: rgb(49, 0, 174) !important;
    transform: translateY(-10px) scale(1.03);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

/* Fix for flip cards (card-1 and card-3) in edit mode */
body.edit-mode .editable-component.card-1,
body.edit-mode .editable-component.card-3 {
    width: 100% !important; /* Take full width of grid cell */
    height: 100% !important; /* Take full height of grid cell */
    min-height: 500px !important; /* Match other cards */
    transform-style: preserve-3d !important;
    perspective: 1000px !important;
}

/* Maintain card dimensions during drag */
.editable-component.card-1.dragging,
.editable-component.card-2.dragging,
.editable-component.card-3.dragging {
    width: 320px !important; /* Fixed size during drag for better UX */
    height: 450px !important;
    min-height: 450px !important;
    max-height: 450px !important;
    transform: rotate(5deg) !important;
}

/* Maintain 3D transforms for flip cards during drag */
.editable-component.card-1.dragging,
.editable-component.card-3.dragging {
    transform-style: preserve-3d !important;
}

/* Maintain card-2 background during drag */
.editable-component.card-2.dragging {
    background-color: rgb(49, 0, 174) !important;
    color: white !important;
    padding: 40px 30px !important;
}

/* Ensure flip-card-inner maintains proper dimensions */
.editable-component.card-1 .flip-card-inner,
.editable-component.card-3 .flip-card-inner {
    width: 100% !important;
    height: 100% !important;
    transform-style: preserve-3d !important;
}

/* Ensure flip-card front and back maintain proper dimensions */
body.edit-mode .editable-component.card-1 .flip-card-front,
body.edit-mode .editable-component.card-1 .flip-card-back,
body.edit-mode .editable-component.card-3 .flip-card-front,
body.edit-mode .editable-component.card-3 .flip-card-back {
    width: 100% !important;
    height: 100% !important;
    min-height: 500px !important; /* Match parent card height */
    position: absolute !important;
    backface-visibility: hidden !important;
}

.editable-component::before {
    display: none;
}

.editable-component:hover::before {
    display: none;
}

/* Dragging States */
.editable-component[draggable="true"] {
    cursor: grab;
}

.editable-component.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
    cursor: grabbing;
    border-color: #007bff !important;
    background-color: rgba(0, 123, 255, 0.1);
}

/* Drop Target Highlight */
.editable-component.drop-target {
    border-color: #28a745 !important;
    background-color: rgba(40, 167, 69, 0.1) !important;
    transform: scale(1.02);
    box-shadow: 0 0 15px rgba(40, 167, 69, 0.3);
}

/* Drag Placeholder - Removed (using swap instead) */

/* Edit Sidebar */
.edit-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: white;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: right 0.3s ease;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.edit-sidebar.active {
    right: 0;
}

/* Left Sidebar Variant */
.edit-sidebar-left {
    left: -400px !important;
    right: auto !important;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1) !important;
    transition: left 0.3s ease !important;
}

.edit-sidebar-left.active {
    left: 0 !important;
    right: auto !important;
}

.sidebar-header {
    background: #007bff;
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h3 {
    margin: 0;
    font-size: 18px;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: background-color 0.3s ease;
}

.close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.sidebar-content {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
}

.sidebar-footer {
    padding: 20px;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    display: flex;
    gap: 10px;
    flex-shrink: 0;
}

.sidebar-footer .btn {
    flex: 1;
}

.component-info {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-item label {
    font-weight: bold;
    color: #495057;
}

.info-item span {
    color: #6c757d;
}

/* Form Styles */
.edit-form {
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.form-group label i {
    color: #007bff;
    margin-right: 6px;
    width: 16px;
}

/* Image Upload Styles */
.image-upload-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.image-preview {
    max-width: 100%;
    max-height: 200px;
    object-fit: cover;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.image-upload-container input[type="file"] {
    display: none;
}

.image-upload-container .btn {
    align-self: flex-start;
}

/* Auto-resize textarea */
.auto-resize {
    resize: none;
    overflow: hidden;
    min-height: 60px;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-actions {
    display: flex;
    gap: 10px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
}

.btn-info:hover {
    background-color: #138496;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
}

/* Component Actions */
.component-actions {
    border-top: 1px solid #dee2e6;
    padding-top: 20px;
}

.component-actions h4 {
    margin-bottom: 15px;
    color: #495057;
    font-size: 16px;
}

.action-buttons {
    display: flex;
    gap: 10px;
}

/* Sidebar Overlay */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Body with sidebar open */
body.sidebar-open {
    overflow: hidden;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 6px;
    color: white;
    font-weight: bold;
    z-index: 1100;
    animation: slideInRight 0.3s ease;
}

.notification-success {
    background-color: #28a745;
}

.notification-error {
    background-color: #dc3545;
}

.notification-info {
    background-color: #17a2b8;
}

.notification-warning {
    background-color: #ffc107;
    color: #212529;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .edit-sidebar {
        width: 100%;
        right: -100%;
    }

    .edit-sidebar-left {
        width: 100% !important;
        left: -100% !important;
    }

    .edit-sidebar-left.active {
        left: 0 !important;
    }

    .sidebar-footer {
        flex-direction: column;
    }

    .editable-component::before {
        font-size: 8px;
        padding: 1px 4px;
    }

    .editable-section::before {
        font-size: 10px;
        padding: 1px 6px;
    }
}

/* Hide edit elements when not in edit mode */
body:not(.edit-mode) .editable-section,
body:not(.edit-mode) .editable-component {
    border: none !important;
    background-color: transparent !important;
    margin: initial !important;
    padding: initial !important;
    cursor: default !important;
    pointer-events: auto !important;
    transform: none !important;
    box-shadow: none !important;
    opacity: 1 !important;
}

body:not(.edit-mode) .editable-section::before,
body:not(.edit-mode) .editable-component::before {
    display: none !important;
}

body:not(.edit-mode) .editable-component:hover {
    transform: none !important;
    box-shadow: none !important;
    border: none !important;
    background-color: transparent !important;
}

/* Reset all child elements when not in edit mode */
body:not(.edit-mode) .editable-component * {
    pointer-events: auto !important;
}

/* Ensure card-2 keeps its original styling when not in edit mode */
body:not(.edit-mode) .editable-component.card-2 {
    background-color: rgb(49, 0, 174) !important;
    color: white !important;
    width: 100% !important; /* Use grid layout width */
    height: 100% !important; /* Use grid layout height */
    min-height: 500px !important; /* Match other cards */
    padding: 40px 30px !important;
}

/* Ensure flip cards keep their original styling when not in edit mode */
body:not(.edit-mode) .editable-component.card-1,
body:not(.edit-mode) .editable-component.card-3 {
    width: 100% !important; /* Use grid layout width */
    height: 100% !important; /* Use grid layout height */
    min-height: 500px !important; /* Match other cards */
    transform-style: preserve-3d !important;
}

/* Floating Edit Button Styles */
.floating-edit-container {
    position: fixed;
    bottom: 85px; /* Position above backToTop button */
    right: 20px;
    z-index: 1001; /* Higher than backToTop (1000) */
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.floating-edit-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

/* Edit Mode Toggle Button - Yellow color, positioned above backToTop */
#editModeToggle {
    position: relative;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 50px;
    border-radius: 50% !important;
    border: none !important;
    background: linear-gradient(135deg, #ffc107, #e0a800) !important; /* Yellow gradient */
    color: #212529 !important; /* Dark text for better contrast on yellow */
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
    transition: all 0.3s ease;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer;
    outline: none !important;
}

#editModeToggle:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
    background: linear-gradient(135deg, #e0a800, #d39e00) !important;
    color: #212529 !important;
}

#editModeToggle:focus {
    box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.25);
    outline: none !important;
}

#editModeToggle i {
    font-size: 16px;
    line-height: 1;
}

/* Edit Mode Toggle Button Active State */
#editModeToggle.active {
    background: linear-gradient(135deg, #28a745, #20c997) !important; /* Green gradient when active (save mode) */
    color: white !important;
    animation: pulseGreen 2s infinite;
}

#editModeToggle.active:hover {
    background: linear-gradient(135deg, #20c997, #17a2b8) !important;
    color: white !important;
}

/* Edit Mode Indicator */
.edit-mode-indicator {
    background: rgba(220, 53, 69, 0.9); /* Red background when in edit mode */
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    white-space: nowrap;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
    animation: slideInRight 0.3s ease;
}

/* Animations */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 .7rem rgba(255, 255, 255, 0.2), 0 0 0 1.0rem rgba(255, 255, 255, 0.2), 0 0 0 3rem rgba(255, 255, 255, 0.2);
    }
    100% {
        box-shadow: 0 0 0 1.2rem rgba(255, 255, 255, 0.2), 0 0 0 3rem rgba(255, 255, 255, 0.2), 0 0 0 3rem rgba(255, 255, 255, 0);
    }
}

@keyframes pulseButton {
    0% {
        box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
    }
    50% {
        box-shadow: 0 4px 20px rgba(220, 53, 69, 0.6);
    }
    100% {
        box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
    }
}

@keyframes pulseGreen {
    0% {
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    }
    50% {
        box-shadow: 0 4px 20px rgba(40, 167, 69, 0.6);
    }
    100% {
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Mobile responsive */
@media (max-width: 768px) {
    .floating-edit-container {
        bottom: 75px; /* Position above backToTop on mobile */
        right: 15px;
    }

    #editModeToggle {
        width: 45px;
        height: 45px;
    }

    #editModeToggle i {
        font-size: 14px;
    }

    .edit-mode-indicator {
        font-size: 11px;
        padding: 4px 8px;
    }
}

/* Disabled Edit Button Styles */
#editModeToggle.disabled {
    background: linear-gradient(135deg, #6c757d, #5a6268) !important; /* Gray gradient when disabled */
    color: #ffffff !important;
    cursor: not-allowed !important;
    opacity: 0.7;
}

#editModeToggle.disabled:hover {
    transform: none !important; /* Remove hover animation */
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3) !important; /* Gray shadow */
    background: linear-gradient(135deg, #6c757d, #5a6268) !important; /* Keep same background */
    color: #ffffff !important;
}
